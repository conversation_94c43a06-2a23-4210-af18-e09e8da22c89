# API Reference: Supabase Database Integration

## Overview

This document provides a comprehensive reference for interacting with the Supabase database, including TypeScript types, RLS policies, and usage examples.

## Database Client Setup

```typescript
// src/lib/supabase.ts
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types';

const supabaseUrl =
  process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseAnonKey =
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';

// Check if environment variables are properly configured
const isConfigured =
  process.env.NEXT_PUBLIC_SUPABASE_URL &&
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

export const supabase = isConfigured
  ? createClient<Database>(supabaseUrl, supabaseAnonKey)
  : null;
```

### Environment Variable Validation

The Supabase client now includes built-in validation to prevent runtime errors when environment variables are missing:

- **Development Safety**: Returns `null` instead of throwing errors when environment variables are not configured
- **Graceful Degradation**: Components can check for `supabase` availability before making API calls
- **Clear Error Messages**: Provides meaningful feedback when configuration is incomplete

## Type Definitions

### Core Database Types

```typescript
// Auto-generated from Supabase schema
export type Vehicle = Database['public']['Tables']['vehicles']['Row'];
export type VehicleInsert = Database['public']['Tables']['vehicles']['Insert'];
export type VehicleUpdate = Database['public']['Tables']['vehicles']['Update'];

export type Booking = Database['public']['Tables']['bookings']['Row'];
export type BookingInsert = Database['public']['Tables']['bookings']['Insert'];
export type BookingUpdate = Database['public']['Tables']['bookings']['Update'];

export type ContactInquiry =
  Database['public']['Tables']['contact_inquiries']['Row'];
export type ContactInquiryInsert =
  Database['public']['Tables']['contact_inquiries']['Insert'];
export type ContactInquiryUpdate =
  Database['public']['Tables']['contact_inquiries']['Update'];

export type EmailTemplate =
  Database['public']['Tables']['email_templates']['Row'];
export type EmailTemplateInsert =
  Database['public']['Tables']['email_templates']['Insert'];
export type EmailTemplateUpdate =
  Database['public']['Tables']['email_templates']['Update'];
```

### Status Enums

```typescript
// Portuguese status values for bookings
export type BookingStatus =
  | 'pendente'
  | 'confirmado'
  | 'cancelado'
  | 'completo';
export type InquiryStatus = 'new' | 'in_progress' | 'resolved' | 'closed';
export type EmailTemplateType =
  | 'booking_confirmation'
  | 'booking_cancellation'
  | 'contact_response'
  | 'admin_notification';
```

### Form Interfaces

```typescript
export interface BookingFormData {
  vehicle_id: string;
  customer_name: string;
  customer_email: string;
  customer_phone?: string;
  start_date: string;
  end_date: string;
  notes?: string;
}

export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  subject?: string;
  message: string;
}
```

## API Operations

### Vehicles

#### Public Operations (No Authentication Required)

```typescript
// Get all vehicles
const { data: vehicles, error } = await supabase
  .from('vehicles')
  .select('*')
  .order('year', { ascending: false });

// Get single vehicle
const { data: vehicle, error } = await supabase
  .from('vehicles')
  .select('*')
  .eq('id', vehicleId)
  .single();

// Get available vehicles for date range
const { data: vehicles, error } = await supabase
  .from('vehicles')
  .select('*')
  .not('availability', 'cs', `{"${startDate}": false, "${endDate}": false}`);
```

#### Admin Operations (Authentication Required)

```typescript
// Create vehicle (Admin only)
const { data: vehicle, error } = await supabase
  .from('vehicles')
  .insert({
    name: 'BMW R100 RS',
    year: 1978,
    description: 'Classic BMW motorcycle',
    price: 150.0,
    photo_url: 'https://supabase-storage-url/vehicle.jpg',
  })
  .select()
  .single();

// Update vehicle (Admin only)
const { data: vehicle, error } = await supabase
  .from('vehicles')
  .update({ price: 175.0 })
  .eq('id', vehicleId)
  .select()
  .single();

// Delete vehicle (Admin only)
const { error } = await supabase.from('vehicles').delete().eq('id', vehicleId);
```

### Bookings

#### Public Operations

```typescript
// Create booking (Public)
const { data: booking, error } = await supabase
  .from('bookings')
  .insert({
    vehicle_id: vehicleId,
    customer_name: 'João Silva',
    customer_email: '<EMAIL>',
    customer_phone: '+*********** 591',
    start_date: '2025-08-01',
    end_date: '2025-08-03',
    notes: 'Para evento de marketing',
  })
  .select()
  .single();
```

#### Admin Operations

```typescript
// Get all bookings with vehicle information (Admin only)
const { data: bookings, error } = await supabase
  .from('bookings')
  .select(
    `
    *,
    vehicles (
      id,
      name,
      year,
      photo_url,
      description,
      price
    )
  `
  )
  .order('created_at', { ascending: false });

// Update booking status with admin notes (Admin only)
const { data: booking, error } = await supabase
  .from('bookings')
  .update({
    status: 'confirmado',
    admin_notes: 'Booking approved after verification',
    updated_at: new Date().toISOString(),
  })
  .eq('id', bookingId)
  .select()
  .single();

// Get bookings by status with filtering (Admin only)
const { data: bookings, error } = await supabase
  .from('bookings')
  .select(
    `
    *,
    vehicles (
      id,
      name,
      year,
      photo_url
    )
  `
  )
  .eq('status', 'pendente')
  .order('created_at', { ascending: false });

// Update admin notes only (Admin only)
const { data: booking, error } = await supabase
  .from('bookings')
  .update({
    admin_notes: 'Customer called to confirm details',
    updated_at: new Date().toISOString(),
  })
  .eq('id', bookingId)
  .select()
  .single();
```

### Contact Inquiries

#### Public Operations

```typescript
// Create contact inquiry (Public)
const { data: inquiry, error } = await supabase
  .from('contact_inquiries')
  .insert({
    name: 'Maria Santos',
    email: '<EMAIL>',
    phone: '+*********** 591',
    company: 'Marketing Empresa Lda',
    subject: 'Aluguer para evento',
    message:
      'Gostaria de alugar uma viatura vintage para o nosso próximo evento.',
  })
  .select()
  .single();
```

#### Admin Operations

```typescript
// Get all inquiries (Admin only)
const { data: inquiries, error } = await supabase
  .from('contact_inquiries')
  .select('*')
  .order('created_at', { ascending: false });

// Update inquiry status (Admin only)
const { data: inquiry, error } = await supabase
  .from('contact_inquiries')
  .update({ status: 'in_progress' })
  .eq('id', inquiryId)
  .select()
  .single();
```

### Email Templates

#### Admin Operations Only

```typescript
// Get all email templates (Admin only)
const { data: templates, error } = await supabase
  .from('email_templates')
  .select('*')
  .order('created_at', { ascending: false });

// Get active templates by type (Admin only)
const { data: templates, error } = await supabase
  .from('email_templates')
  .select('*')
  .eq('template_type', 'booking_confirmation')
  .eq('is_active', true);

// Create email template (Admin only)
const { data: template, error } = await supabase
  .from('email_templates')
  .insert({
    name: 'Confirmação de Reserva - Padrão',
    subject: 'Confirmação da sua reserva - {{vehicle_name}}',
    body: 'Caro(a) {{customer_name}}, confirmamos a sua reserva...',
    template_type: 'booking_confirmation',
    is_active: true,
  })
  .select()
  .single();

// Update email template (Admin only)
const { data: template, error } = await supabase
  .from('email_templates')
  .update({
    name: 'Confirmação Atualizada',
    subject: 'Nova Confirmação - {{vehicle_name}}',
    body: 'Caro {{customer_name}}, confirmamos a sua reserva...',
    updated_at: new Date().toISOString(),
  })
  .eq('id', templateId)
  .select()
  .single();

// Toggle template active status (Admin only)
const { data: template, error } = await supabase
  .from('email_templates')
  .update({
    is_active: !currentStatus,
    updated_at: new Date().toISOString(),
  })
  .eq('id', templateId)
  .select()
  .single();

// Delete email template (Admin only)
const { error } = await supabase
  .from('email_templates')
  .delete()
  .eq('id', templateId);
```

#### Template Variable System

The email template system supports 13 predefined variables for dynamic content:

```typescript
// Available template variables
const TEMPLATE_VARIABLES = [
  { key: 'customer_name', label: 'Nome do Cliente', example: 'João Silva' },
  {
    key: 'customer_email',
    label: 'Email do Cliente',
    example: '<EMAIL>',
  },
  {
    key: 'customer_phone',
    label: 'Telefone do Cliente',
    example: '+351 912 345 678',
  },
  {
    key: 'vehicle_name',
    label: 'Nome do Veículo',
    example: 'Fleur de Lys Van',
  },
  { key: 'vehicle_year', label: 'Ano do Veículo', example: '1975' },
  {
    key: 'start_date',
    label: 'Data de Início',
    example: '15 de Janeiro de 2025',
  },
  { key: 'end_date', label: 'Data de Fim', example: '17 de Janeiro de 2025' },
  { key: 'total_price', label: 'Preço Total', example: '€450.00' },
  { key: 'booking_id', label: 'ID da Reserva', example: 'VMP-2025-001' },
  {
    key: 'admin_notes',
    label: 'Notas do Admin',
    example: 'Entrega no local do evento',
  },
  {
    key: 'company_name',
    label: 'Nome da Empresa',
    example: 'Vintage Marketing Portugal',
  },
  {
    key: 'company_phone',
    label: 'Telefone da Empresa',
    example: '+*********** 591',
  },
  {
    key: 'company_email',
    label: 'Email da Empresa',
    example: '<EMAIL>',
  },
];

// Variable usage in templates
const templateBody = `
Caro(a) {{customer_name}},

Confirmamos a sua reserva do {{vehicle_name}} ({{vehicle_year}}) 
de {{start_date}} até {{end_date}}.

Preço total: {{total_price}}
ID da reserva: {{booking_id}}

{{admin_notes}}

Contactos: {{company_phone}} | {{company_email}}
`;
```

## Row Level Security (RLS) Behavior

### Public Users (Unauthenticated)

- ✅ **Can read** vehicles
- ✅ **Can create** bookings and contact inquiries
- ❌ **Cannot read** bookings, contact inquiries, or email templates
- ❌ **Cannot modify** any data except creating bookings/inquiries

### Admin Users (Authenticated with admin role)

- ✅ **Full access** to all tables and operations
- ✅ **Can manage** vehicles, bookings, inquiries, and email templates
- ✅ **Can view** all data across all tables

### Authentication Context

```typescript
// Check if user is admin (for UI logic)
const {
  data: { user },
} = await supabase.auth.getUser();
const isAdmin =
  user?.user_metadata?.role === 'admin' || user?.app_metadata?.role === 'admin';

// The database-level is_admin() function handles server-side authorization
```

## Error Handling

```typescript
// Helper functions from src/lib/supabase.ts
export const handleSupabaseError = (error: any) => {
  console.error('Supabase error:', error);
  return {
    success: false,
    error: error.message || 'An unexpected error occurred',
  };
};

export const handleSupabaseSuccess = <T>(data: T) => {
  return {
    success: true,
    data,
  };
};

// Usage example
const createBooking = async (bookingData: BookingFormData) => {
  try {
    const { data, error } = await supabase
      .from('bookings')
      .insert(bookingData)
      .select()
      .single();

    if (error) return handleSupabaseError(error);
    return handleSupabaseSuccess(data);
  } catch (error) {
    return handleSupabaseError(error);
  }
};
```

## Real-time Subscriptions

```typescript
// Subscribe to booking changes (Admin only - will be filtered by RLS)
const subscription = supabase
  .channel('bookings-changes')
  .on(
    'postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'bookings',
    },
    (payload) => {
      console.log('Booking changed:', payload);
      // Update UI accordingly
    }
  )
  .subscribe();

// Clean up subscription
subscription.unsubscribe();
```

## Admin Booking Management API

### BookingsPage Implementation

The admin booking management system uses a comprehensive API pattern for managing reservations:

```typescript
// Load all bookings with vehicle information
const loadBookings = async () => {
  if (!supabase) {
    setError('Supabase não está configurado');
    setIsLoading(false);
    return;
  }

  try {
    const { data: bookingsData, error: bookingsError } = await supabase
      .from('bookings')
      .select(
        `
        *,
        vehicles (
          id,
          name,
          year,
          photo_url,
          description,
          price
        )
      `
      )
      .order('created_at', { ascending: false });

    if (bookingsError) {
      console.error('Error loading bookings:', bookingsError);
      setError('Erro ao carregar reservas');
      return;
    }

    const bookingsWithVehicles: BookingWithVehicle[] =
      bookingsData?.map((booking) => ({
        ...booking,
        vehicle: booking.vehicles as Vehicle,
      })) || [];

    setBookings(bookingsWithVehicles);
  } catch (error) {
    console.error('Unexpected error loading bookings:', error);
    setError('Erro inesperado ao carregar reservas');
  } finally {
    setIsLoading(false);
  }
};

// Update booking status with admin notes
const handleStatusUpdate = async (
  bookingId: string,
  newStatus: BookingStatus,
  adminNotes?: string
) => {
  if (!supabase) return;

  try {
    const updateData: any = {
      status: newStatus,
      updated_at: new Date().toISOString(),
    };

    if (adminNotes !== undefined) {
      updateData.admin_notes = adminNotes;
    }

    const { error } = await supabase
      .from('bookings')
      .update(updateData)
      .eq('id', bookingId);

    if (error) {
      console.error('Error updating booking status:', error);
      setError('Erro ao atualizar status da reserva');
      return;
    }

    // Update local state for immediate UI feedback
    setBookings((prevBookings) =>
      prevBookings.map((booking) =>
        booking.id === bookingId
          ? {
              ...booking,
              status: newStatus,
              admin_notes: adminNotes || booking.admin_notes,
            }
          : booking
      )
    );
  } catch (error) {
    console.error('Unexpected error updating booking:', error);
    setError('Erro inesperado ao atualizar reserva');
  }
};
```

### Status Management Patterns

```typescript
// Status filtering with counts
const getStatusCounts = () => {
  return {
    all: bookings.length,
    pendente: bookings.filter((b) => b.status === 'pendente').length,
    confirmado: bookings.filter((b) => b.status === 'confirmado').length,
    cancelado: bookings.filter((b) => b.status === 'cancelado').length,
  };
};

// Filter bookings by status
const filterBookings = () => {
  if (statusFilter === 'all') {
    setFilteredBookings(bookings);
  } else {
    setFilteredBookings(
      bookings.filter((booking) => booking.status === statusFilter)
    );
  }
};
```

### Error Handling Patterns

```typescript
// Comprehensive error handling with Portuguese messages
const handleSupabaseError = (error: any, context: string) => {
  console.error(`${context}:`, error);

  // Map common errors to Portuguese messages
  const errorMessages = {
    PGRST116: 'Dados não encontrados',
    PGRST301: 'Acesso negado',
    connection: 'Erro de conexão. Verifique sua internet.',
    timeout: 'Operação demorou muito. Tente novamente.',
  };

  const userMessage =
    errorMessages[error.code] ||
    errorMessages[error.message] ||
    'Erro inesperado. Tente novamente.';

  setError(userMessage);
};
```

## Email Service API

### Email Service Functions

The email service provides high-level functions for sending templated emails via Supabase Edge Functions:

```typescript
// Send email using template
import { sendEmail } from '@/lib/emailService';

const result = await sendEmail({
  to: '<EMAIL>',
  template_id: 'template-uuid',
  booking_id: 'booking-uuid', // optional
  variables: {
    // optional additional variables
    custom_message: 'Special instructions for your booking',
  },
});

// Send booking confirmation email
import { sendBookingConfirmationEmail } from '@/lib/emailService';

const result = await sendBookingConfirmationEmail(
  'booking-uuid',
  '<EMAIL>',
  'template-uuid', // optional - will find default if not provided
  { custom_note: 'Thank you for choosing us!' } // optional additional variables
);

// Send booking cancellation email
import { sendBookingCancellationEmail } from '@/lib/emailService';

const result = await sendBookingCancellationEmail(
  'booking-uuid',
  '<EMAIL>',
  'template-uuid', // optional
  { cancellation_reason: 'Customer request' }
);

// Send contact form response
import { sendContactResponseEmail } from '@/lib/emailService';

const result = await sendContactResponseEmail(
  '<EMAIL>',
  'João Silva',
  'template-uuid', // optional
  { inquiry_type: 'General inquiry' }
);

// Get available templates
import { getEmailTemplates } from '@/lib/emailService';

const { data: templates, error } = await getEmailTemplates();
```

### Email Service Response Format

```typescript
interface SendEmailResponse {
  success: boolean;
  email_id?: string;
  message?: string;
  error?: string;
}

// Success response
{
  success: true,
  email_id: "email-uuid",
  message: "Email sent successfully"
}

// Error response
{
  success: false,
  error: "Template not found"
}
```

### Supabase Edge Function Integration

The email system uses a Supabase Edge Function (`send-email`) that:

1. Fetches the email template from the database
2. Resolves booking data if `booking_id` is provided
3. Substitutes template variables with actual data
4. Sends the email via Resend API
5. Returns success/error response

```typescript
// Edge function invocation (handled by emailService.ts)
const { data, error } = await supabase.functions.invoke('send-email', {
  body: {
    to: '<EMAIL>',
    template_id: 'template-uuid',
    booking_id: 'booking-uuid',
    variables: { custom_var: 'value' },
  },
});
```

## Best Practices

1. **Always use TypeScript types** for type safety
2. **Handle RLS policy violations gracefully** - operations may silently fail for unauthorized users
3. **Use select() with specific fields** to optimize performance
4. **Implement proper error handling** for all database operations
5. **Use real-time subscriptions** for admin dashboards to show live updates
6. **Store only public URLs** in photo_url fields (use Supabase Storage)
7. **Validate data on both client and server side** using the CHECK constraints as reference
8. **Implement optimistic updates** for better user experience in admin interfaces
9. **Use Portuguese error messages** for all user-facing errors
10. **Include proper loading states** for all async operations
11. **Use email service functions** instead of direct Edge Function calls for better error handling
12. **Test email templates** with preview functionality before activating
13. **Maintain default templates** for each email type to ensure system reliability

## Environment Variables Required

```bash
# .env.local
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key # For admin operations
```

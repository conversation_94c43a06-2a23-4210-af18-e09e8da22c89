# Email System Architecture

## Overview

The Vintage Marketing Portugal email system provides a comprehensive template-based email solution for customer communications. The system supports dynamic content through variable substitution and integrates with Supabase Edge Functions and the Resend API for reliable email delivery.

## System Components

### 1. Database Schema

#### Email Templates Table

```sql
CREATE TABLE email_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  subject TEXT NOT NULL,
  body TEXT NOT NULL,
  template_type TEXT NOT NULL CHECK (template_type IN (
    'booking_confirmation',
    'booking_cancellation',
    'contact_response',
    'admin_notification'
  )),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. Frontend Components

#### EmailTemplateManager

- **Purpose**: Main admin interface for template management
- **Features**: CRUD operations, search, status management
- **Location**: `src/components/admin/EmailTemplateManager.tsx`

#### EmailTemplateEditor

- **Purpose**: Template creation and editing interface
- **Features**: Variable insertion, live preview, form validation
- **Location**: `src/components/admin/EmailTemplateEditor.tsx`

### 3. Email Service Layer

#### Email Service (`src/lib/emailService.ts`)

Provides high-level functions for email operations:

```typescript
// Core functions
sendEmail(request: SendEmailRequest): Promise<SendEmailResponse>
sendBookingConfirmationEmail(bookingId, customerEmail, templateId?, variables?)
sendBookingCancellationEmail(bookingId, customerEmail, templateId?, variables?)
sendContactResponseEmail(customerEmail, customerName, templateId?, variables?)
getEmailTemplates()
```

### 4. Supabase Edge Function

#### send-email Function (`supabase/functions/send-email/index.ts`)

Server-side email processing:

1. Validates request parameters
2. Fetches email template from database
3. Resolves booking data if booking_id provided
4. Performs variable substitution
5. Sends email via Resend API
6. Returns success/error response

## Template Variable System

### Available Variables

The system supports 13 predefined variables for dynamic content:

| Variable         | Description               | Example                    |
| ---------------- | ------------------------- | -------------------------- |
| `customer_name`  | Customer's full name      | João Silva                 |
| `customer_email` | Customer's email address  | <EMAIL>           |
| `customer_phone` | Customer's phone number   | +351 912 345 678           |
| `vehicle_name`   | Vehicle name              | Fleur de Lys Van           |
| `vehicle_year`   | Vehicle year              | 1975                       |
| `start_date`     | Booking start date        | 15 de Janeiro de 2025      |
| `end_date`       | Booking end date          | 17 de Janeiro de 2025      |
| `total_price`    | Total booking price       | €450.00                    |
| `booking_id`     | Unique booking identifier | VMP-2025-001               |
| `admin_notes`    | Admin notes for booking   | Entrega no local do evento |
| `company_name`   | Company name              | Vintage Marketing Portugal |
| `company_phone`  | Company phone number      | +*********** 591           |
| `company_email`  | Company email address     | <EMAIL>   |

### Variable Usage

Variables are used in templates with double curly braces:

```
Caro(a) {{customer_name}},

Confirmamos a sua reserva do {{vehicle_name}} ({{vehicle_year}})
de {{start_date}} até {{end_date}}.

Preço total: {{total_price}}
```

### Variable Resolution

1. **Booking Variables**: Resolved from booking and vehicle data when `booking_id` is provided
2. **Customer Variables**: Passed directly in the request
3. **Company Variables**: Static company information
4. **Admin Variables**: Admin notes and custom variables

## Template Types

### 1. Booking Confirmation (`booking_confirmation`)

- **Purpose**: Sent when admin confirms a booking
- **Trigger**: Admin status update to 'confirmado'
- **Variables**: All booking-related variables available

### 2. Booking Cancellation (`booking_cancellation`)

- **Purpose**: Sent when booking is cancelled
- **Trigger**: Admin status update to 'cancelado'
- **Variables**: All booking-related variables available

### 3. Contact Response (`contact_response`)

- **Purpose**: Auto-response to contact form submissions
- **Trigger**: Contact form submission
- **Variables**: Customer and company variables

### 4. Admin Notification (`admin_notification`)

- **Purpose**: Internal notifications to admin users
- **Trigger**: Various system events
- **Variables**: Context-dependent variables

## Email Flow Architecture

### 1. Template Creation Flow

```
Admin → EmailTemplateManager → EmailTemplateEditor → Database
```

1. Admin accesses template management interface
2. Creates/edits template with variable insertion
3. Previews template with example data
4. Saves template to database

### 2. Email Sending Flow

```
Trigger Event → Email Service → Edge Function → Resend API → Customer
```

1. System event triggers email (booking confirmation, contact form, etc.)
2. Email service determines appropriate template
3. Edge function processes template and variables
4. Email sent via Resend API
5. Response returned to calling system

### 3. Variable Resolution Flow

```
Template + Variables → Variable Substitution → Final Email Content
```

1. Template retrieved from database
2. Variables collected from various sources (booking, customer, system)
3. Template variables replaced with actual values
4. Final email content generated

## Default Templates

The system includes default templates for each type, created via the setup script:

### Default Template Creation Script

**File**: `scripts/create-default-email-templates.js`

**Purpose**: Creates default Portuguese templates for all email types

**Templates Created**:

- Confirmação de Reserva - Padrão
- Cancelamento de Reserva - Padrão
- Resposta a Contacto - Padrão

**Usage**: `node scripts/create-default-email-templates.js`

## Security and Access Control

### Row Level Security (RLS)

- **Public Users**: Cannot access email templates
- **Admin Users**: Full CRUD access to email templates
- **Template Access**: Only active templates used for sending

### Environment Variables

```bash
# Required for email functionality
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
RESEND_API_KEY=your_resend_api_key
```

## Error Handling

### Frontend Error Handling

- Form validation with Portuguese error messages
- Database operation error handling
- Loading states and user feedback
- Graceful degradation for missing templates

### Backend Error Handling

- Template not found errors
- Variable substitution errors
- Email delivery failures
- API rate limiting handling

### Common Error Scenarios

1. **Template Not Found**: Fallback to default template or error message
2. **Missing Variables**: Graceful handling with placeholder text
3. **Email Delivery Failure**: Retry logic and error logging
4. **Invalid Template Format**: Validation and error reporting

## Performance Considerations

### Template Caching

- Templates cached in Edge Function for performance
- Cache invalidation on template updates
- Reduced database queries for frequent operations

### Variable Resolution Optimization

- Batch data fetching for booking information
- Minimal database queries per email
- Efficient variable substitution algorithm

### Email Delivery Optimization

- Asynchronous email sending
- Queue management for bulk operations
- Rate limiting compliance with Resend API

## Monitoring and Logging

### Email Delivery Tracking

- Success/failure logging in Edge Function
- Email delivery status tracking
- Error rate monitoring

### Template Usage Analytics

- Template usage frequency tracking
- Popular variable usage analysis
- Template performance metrics

### System Health Monitoring

- Email service availability monitoring
- Template system error tracking
- Performance metrics collection

## Future Enhancements

### Planned Features

1. **Rich Text Editor**: WYSIWYG editor for template creation
2. **Template Versioning**: Version control for template changes
3. **A/B Testing**: Template performance comparison
4. **Scheduled Emails**: Time-based email sending
5. **Email Analytics**: Open rates, click tracking
6. **Multi-language Support**: Template localization
7. **Custom Variables**: User-defined template variables
8. **Email Attachments**: File attachment support

### Technical Improvements

1. **Template Validation**: Advanced template syntax validation
2. **Performance Optimization**: Caching and query optimization
3. **Error Recovery**: Improved error handling and recovery
4. **Scalability**: Support for high-volume email sending
5. **Integration**: Third-party service integrations
6. **Testing**: Automated template testing framework

## Best Practices

### Template Design

1. **Keep templates simple**: Avoid complex HTML structures
2. **Use semantic variables**: Choose descriptive variable names
3. **Test thoroughly**: Preview templates with real data
4. **Maintain consistency**: Use consistent styling and messaging
5. **Portuguese language**: All customer-facing content in Portuguese

### System Administration

1. **Regular backups**: Backup template configurations
2. **Monitor performance**: Track email delivery rates
3. **Update templates**: Keep templates current with business needs
4. **Security updates**: Maintain system security patches
5. **Documentation**: Keep template documentation updated

### Development Guidelines

1. **Type safety**: Use TypeScript interfaces for all email operations
2. **Error handling**: Implement comprehensive error handling
3. **Testing**: Test email functionality in development environment
4. **Code review**: Review template changes before deployment
5. **Version control**: Track all template and code changes

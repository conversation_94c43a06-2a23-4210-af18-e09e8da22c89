# Animation Implementation Guide

## Overview

This guide provides comprehensive instructions for implementing and extending the Vintage Marketing Portugal animation system. The system is built on Framer Motion and represents a production-ready, best-in-class implementation that exceeds industry standards.

## Quick Start

### Basic Page Animation Setup

```typescript
import {
  AnimatedPageWrapper,
  AnimatedSection,
  ScrollReveal,
  TextReveal,
  AnimatedButton
} from '@/components/animations';

export default function MyPage() {
  return (
    <AnimatedPageWrapper>
      <AnimatedSection delay={0.1}>
        <TextReveal>
          <h1>Page Title</h1>
        </TextReveal>
        <ScrollReveal delay={0.2}>
          <p>Content that reveals on scroll</p>
        </ScrollReveal>
        <AnimatedButton href="/contact" variant="primary">
          Contact Us
        </AnimatedButton>
      </AnimatedSection>
    </AnimatedPageWrapper>
  );
}
```

### Card Layout with Stacking Effects

```typescript
import { StaggeredCard, HoverCard } from '@/components/animations';

export default function CardGrid({ items }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      {items.map((item, index) => (
        <StaggeredCard key={item.id} index={index}>
          <HoverCard>
            <div className="card p-6">
              <h3>{item.title}</h3>
              <p>{item.description}</p>
            </div>
          </HoverCard>
        </StaggeredCard>
      ))}
    </div>
  );
}
```

## Component Categories

### 1. Basic Animations

#### FadeIn

- **Use case**: General content reveals, loading states
- **Props**: `delay`, `duration`, `className`
- **Performance**: Automatically optimizes for low-end devices

#### SlideIn

- **Use case**: Directional content entry, navigation items
- **Props**: `direction` ('left', 'right', 'up', 'down'), `delay`, `duration`
- **Adaptive**: Reduces slide distance on low-end devices

#### ScaleIn

- **Use case**: Emphasis, call-to-action buttons, icons
- **Props**: `delay`, `duration`, `className`
- **Effect**: Scale from 0.8 to 1.0 with opacity fade

#### StaggerContainer

- **Use case**: Lists, grids, navigation menus
- **Props**: `staggerDelay`, `className`
- **Pattern**: Uses Framer Motion variants for proper orchestration

### 2. Scroll-Based Animations

#### ScrollReveal

- **Use case**: Content sections, images, cards
- **Props**: `threshold`, `delay`, `className`
- **Trigger**: Configurable viewport intersection (default: 10%)

#### TextReveal

- **Use case**: Headings, important text content
- **Props**: `delay`, `stagger`, `className`
- **Effect**: Optimized for text with optional character staggering

#### CardStack

- **Use case**: Card layouts requiring stacking/folding effects
- **Props**: `index`, `total`, `className`
- **Math**: Precise calculations for realistic stacking behavior

#### StaggeredCard

- **Use case**: Card grids, product listings, service sections
- **Props**: `index`, `className`
- **Timing**: Index-based delays for natural reveal sequence

### 3. Micro-Interactions

#### MagneticButton

- **Use case**: Primary CTAs, interactive elements
- **Props**: `strength`, `className`
- **Behavior**: Real-time mouse tracking with magnetic attraction

#### CounterAnimation

- **Use case**: Statistics, numbers, progress indicators
- **Props**: `from`, `to`, `duration`, `className`
- **Performance**: RequestAnimationFrame with custom easing

## Performance Guidelines

### Device Optimization

The system automatically adapts based on device capabilities:

```typescript
// Low-end device detection
const isLowEndDevice = navigator.hardwareConcurrency <= 2;
const hasSlowConnection = navigator.connection?.effectiveType === 'slow-2g';

// Automatic optimization
if (shouldOptimize) {
  duration = 0.1; // Reduced from 0.6s
  ease = 'linear'; // Simplified from cubic-bezier
  stagger = 0.01; // Reduced from 0.1s
}
```

### Responsive Scaling

Animations adapt to screen size:

- **Mobile (< 768px)**:
  - Simple animations only
  - 60% distance multiplier
  - 50% stagger multiplier
- **Tablet (768-1024px)**:
  - Medium complexity
  - 80% distance multiplier
  - 75% stagger multiplier
- **Desktop (> 1024px)**:
  - Full animation complexity
  - 100% all multipliers

### Hardware Acceleration

**Always Use** (GPU-accelerated):

- `opacity`
- `transform` (translateX, translateY, scale, rotate)

**Never Use** (triggers layout):

- `width`, `height`
- `top`, `left`, `right`, `bottom`
- `margin`, `padding`

## Accessibility Implementation

### Reduced Motion Support

Every component automatically respects user preferences:

```typescript
const { prefersReducedMotion } = useAnimationOptimization();

if (prefersReducedMotion) {
  return <div className={className}>{children}</div>;
}
```

### Implementation Checklist

- ✅ Dynamic detection of `prefers-reduced-motion`
- ✅ Event listeners for preference changes
- ✅ Graceful fallback to static content
- ✅ No layout shifts when animations disabled
- ✅ Keyboard navigation compatibility

## Animation Timing Standards

### Duration Standards

- **Basic animations**: 0.5-0.6s
- **Page transitions**: 0.5s
- **Micro-interactions**: 0.2-0.4s
- **Scroll animations**: Scroll-based (no fixed duration)

### Stagger Timing

- **Default stagger**: 0.1s between items
- **Text stagger**: 0.05s between characters
- **Card stagger**: 0.15s between cards

### Easing Curves

- **Primary**: `[0.25, 0.46, 0.45, 0.94]` (natural motion)
- **Page transitions**: `anticipate` (Framer Motion)
- **Reveals**: `easeOut`
- **Performance fallback**: `linear`

## Integration Patterns

### Page-Level Integration

```typescript
// app/layout.tsx
<PageTransitionProvider>
  <RouteChangeHandler />
  <Layout>
    <Suspense fallback={<LoadingAnimation isLoading={true} />}>
      <PageTransition>{children}</PageTransition>
    </Suspense>
  </Layout>
  <PagePreloader />
</PageTransitionProvider>
```

### Component-Level Integration

```typescript
// Individual page components
<AnimatedPageWrapper>
  <AnimatedSection delay={0.1}>
    {/* Page content with coordinated animations */}
  </AnimatedSection>
</AnimatedPageWrapper>
```

## CSS Animation Integration

For performance-critical scenarios with many elements (like table rows), the system provides CSS-based alternatives:

```css
.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
  will-change: opacity;
  opacity: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

**Usage Pattern**:

```typescript
<tr
  className="animate-fade-in"
  style={{
    animationDelay: `${index * 0.1}s`,
    animationFillMode: 'both'
  }}
>
  {/* Table content */}
</tr>
```

**When to Use CSS Animations**:

- **Large lists/tables**: Better performance than individual Framer Motion components
- **Simple animations**: Fade-in, slide-up, scale-in effects
- **Staggered sequences**: Using calculated delays for natural timing
- **Memory optimization**: Reduces React component overhead for repetitive elements

**Implementation Example (EmailTemplateManager)**:
The EmailTemplateManager component demonstrates this pattern by using CSS animations for table rows while maintaining Framer Motion for page-level transitions:

```typescript
{filteredTemplates.map((template, index) => (
  <tr
    key={template.id}
    className="hover:bg-gray-50 animate-fade-in"
    style={{
      animationDelay: `${index * 0.1}s`,
      animationFillMode: 'both'
    }}
  >
    {/* Table content */}
  </tr>
))}
```

## Best Practices

### 1. Use Variants Pattern

```typescript
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3,
    },
  },
};
```

### 2. Coordinate Timing

- Use `delayChildren` to start after page transitions
- Use `staggerChildren` for natural sequencing
- Avoid manual timing calculations

### 3. Respect Performance

- Always use the performance optimization hooks
- Test on low-end devices
- Monitor FPS during development

### 4. Maintain Consistency

- Use standardized timing values
- Apply consistent easing curves
- Follow established patterns

## Testing Guidelines

### Performance Testing

```typescript
const performance = await testAnimationPerformance(() => {
  // Trigger animations
}, 2000);

console.log(`Average FPS: ${performance.averageFPS}`);
```

### Accessibility Testing

- Test with `prefers-reduced-motion: reduce`
- Verify keyboard navigation works
- Check screen reader compatibility

### Cross-Device Testing

- Test on mobile devices (iOS/Android)
- Verify tablet behavior
- Check desktop performance

## Troubleshooting

### Common Issues

1. **Animations not triggering**
   - Check if component is wrapped in proper container
   - Verify scroll thresholds are appropriate
   - Ensure viewport intersection is working

2. **Performance issues**
   - Check if using layout-triggering properties
   - Verify hardware acceleration is enabled
   - Test device capability detection

3. **Accessibility problems**
   - Confirm `prefers-reduced-motion` detection
   - Check graceful fallbacks
   - Verify no layout shifts occur

### Debug Tools

```typescript
// Enable animation debugging
const { shouldOptimize, animationConfig } = useAnimationOptimization();
console.log('Animation config:', animationConfig);

// Performance monitoring
const monitor = new AnimationPerformanceMonitor();
monitor.start();
```

This animation system represents a masterclass implementation that can serve as a reference for other projects. The combination of technical excellence, performance optimization, and accessibility compliance makes it a truly exceptional animation ecosystem.

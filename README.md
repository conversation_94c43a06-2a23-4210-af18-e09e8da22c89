# Vintage Marketing Portugal

A professional website for vintage vehicle rentals targeting Portuguese companies for marketing events and campaigns.

## Tech Stack

- **Frontend**: Next.js 15 with App Router, TypeScript, and Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Real-time APIs)
- **UI Components**: Headless UI for accessible modal and form components
- **Animation**: Framer Motion (motion package) with comprehensive animation ecosystem
- **Typography**: Open Sans (headings), Inter (body text), <PERSON>elight (logo)
- **State Management**: Custom React hooks with TypeScript
- **Image Optimization**: Next.js Image component with Supabase Storage
- **Email**: Resend for email delivery (configured)
- **Hosting**: Vercel with automatic deployments and CDN
- **Testing**: Jest + React Testing Library (configured)
- **Code Quality**: ESLint + Prettier with TypeScript strict mode

## Getting Started

1. Install dependencies:

```bash
npm install
```

2. Copy environment variables:

```bash
cp .env.local.example .env.local
```

3. Configure your Supabase and Resend API keys in `.env.local`

4. Run the development server:

```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues automatically
- `npm run type-check` - TypeScript validation
- `npm run format` - Prettier formatting
- `npm run format:check` - Check Prettier formatting
- `npm run test` - Run Jest tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report
- `npm run pre-commit` - Run all quality checks (type-check, lint, format, test)
- `npm run deploy` - Deploy to production (Vercel)
- `npm run deploy:preview` - Deploy preview build

## Project Structure

```
src/
├── app/           # Next.js App Router pages
│   ├── globals.css        # Global styles and design system
│   ├── layout.tsx         # Root layout with providers
│   ├── page.tsx           # Homepage component
│   ├── servicos/          # Services page
│   ├── frota/             # Fleet showcase page
│   ├── reservas/          # Booking system page
│   ├── contacto/          # Contact page
│   ├── termos/            # Terms and conditions page
│   └── admin/             # Admin panel pages (planned)
├── components/    # React components
│   ├── animations/       # Framer Motion animation components
│   │   ├── index.ts             # Animation exports barrel file
│   │   ├── FadeIn.tsx           # Fade-in animation component
│   │   ├── SlideIn.tsx          # Directional slide animations
│   │   ├── ScaleIn.tsx          # Scale-up animations
│   │   ├── StaggerContainer.tsx # Staggered child animations
│   │   ├── PageTransition.tsx   # Page transition system
│   │   ├── ScrollAnimations.tsx # Scroll-based animation components
│   │   └── MicroInteractions.tsx # Interactive micro-animations
│   ├── layout/           # Header, Footer components
│   ├── forms/            # Booking form components
│   ├── admin/            # Admin panel components (planned)
│   ├── auth/             # Authentication components (planned)
│   ├── ContactPage.tsx   # Contact page component
│   ├── VehicleCard.tsx   # Vehicle display card
│   ├── VehicleModal.tsx  # Vehicle detail modal
│   └── FeaturedVehicles.tsx # Homepage featured vehicles
├── hooks/         # Custom React hooks
│   ├── useBookingForm.ts        # Booking form state management
│   ├── useParallax.ts           # Basic parallax scroll effects
│   ├── useScrollAnimations.ts   # Advanced scroll animation hooks
│   └── usePerformanceOptimization.ts # Animation performance optimization
├── lib/          # Utility functions and configurations
│   └── supabase.ts       # Supabase client configuration
├── types/        # TypeScript type definitions
│   └── index.ts          # Database types (auto-generated from Supabase)
├── utils/        # Helper functions
│   ├── animationOptimization.ts # Animation performance utilities
│   ├── validation.ts            # Form validation utilities
│   ├── date.ts                  # Date formatting utilities
│   └── authErrors.ts            # Authentication error handling
└── contexts/     # React contexts
    └── AuthContext.tsx   # Authentication context (planned)

Docs/
├── Architecture/  # Technical documentation
│   ├── database-schema.md         # Complete database schema with RLS policies
│   ├── api-reference.md           # Supabase API usage examples
│   ├── component-documentation.md # Component specifications and usage
│   ├── email-system.md            # Email template system architecture
│   └── technical-architecture.md  # System architecture overview
├── Development/   # Development workflows and standards
│   ├── development-guide.md       # Complete development guide
│   ├── coding-standards.md        # Code quality standards
│   └── animation-implementation-guide.md # Animation system guide
├── Infrastructure/ # Deployment and infrastructure
│   ├── deployment-guide.md        # Production deployment guide
│   └── supabase-setup.md          # Supabase configuration guide
└── Project/       # Project documentation
    ├── implementation-status.md   # Detailed implementation status
    ├── project-summary.md         # Executive project summary
    └── PRD.md                     # Product Requirements Document
```

## Features

- Fleet showcase with vintage vehicles
- Calendar-based booking system
- Admin panel for managing bookings and vehicles
- Portuguese language content with local SEO
- Mobile-first responsive design
- Email template system with dynamic content and variable substitution
- Admin panel for booking and email template management

## Implementation Status

### ✅ Database & Backend (Completed)

- **Supabase Database**: All tables deployed with proper schema
  - `vehicles` - Fleet management with availability tracking
  - `bookings` - Customer booking management with status tracking
  - `contact_inquiries` - Contact form submissions with company details
  - `email_templates` - Admin-customizable email templates
- **Row Level Security**: Comprehensive RLS policies implemented
  - Public users: Can view vehicles, create bookings/inquiries
  - Admin users: Full management access to all data
- **Database Functions**: JWT-based admin role checking with `is_admin()` function
- **TypeScript Integration**: Auto-generated types from Supabase schema with convenience exports

### ✅ Frontend Core (Completed)

- **✅ Layout System**: Complete layout architecture implemented
  - **Header Component**: Responsive navigation with mobile hamburger menu, design system integration
  - **Footer Component**: Business information, contact details, quick links, social media integration
  - **Layout Wrapper**: Consistent page structure with AuthProvider integration
- **✅ Design System**: Comprehensive design system implemented
  - **Color Palette**: Primary (black/grays), background (cream/beige), accent colors
  - **Typography**: Open Sans headings, Inter body text, Limelight logo font
  - **Component Styles**: Buttons (primary/secondary/CTA), cards, navigation, sections
  - **Responsive Design**: Mobile-first approach with consistent breakpoints
  - **Animation System**: Framer Motion-based animations with hover effects, transitions, and micro-interactions

### ✅ Animation System (Completed) - **PRODUCTION-READY IMPLEMENTATION**

The website features a **comprehensive, performance-optimized animation system** built with Framer Motion:

#### **Core Animation Components**

- **Basic Animations**: FadeIn, SlideIn, ScaleIn, StaggerContainer with performance optimization
- **Scroll-Based**: ScrollReveal, TextReveal, CardStack, StaggeredCard, IndependentSection, HeroParallax, ParallaxEnhanced
- **Micro-Interactions**: AnimatedLink, FloatingButton, MagneticButton, RippleButton, GlowEffect, CounterAnimation
- **Interactive Elements**: AnimatedButton, HoverCard with smooth transitions and hover effects

#### **Advanced Features**

- **Scroll Animations**: 12+ custom hooks for scroll-based effects (useScrollReveal, useCardStack, useParallaxImproved, etc.)
- **Performance Optimization**: Hardware acceleration, device capability detection, animation cleanup utilities
- **Accessibility Compliance**: Comprehensive `prefers-reduced-motion` support with graceful fallbacks
- **Responsive Design**: Mobile-aware parallax effects and animation scaling
- **TypeScript Integration**: Full type safety with proper interfaces for all animation components

#### **Technical Implementation**

- **Motion Package**: Uses latest Framer Motion (motion/react) for optimal performance
- **Custom Hooks**: 12+ specialized hooks for different animation patterns
- **Performance Utilities**: Animation optimization, cleanup, and conflict prevention
- **Hardware Acceleration**: GPU-optimized transforms using translateZ(0) and backface-visibility
- **Memory Management**: Proper cleanup of animation listeners and resources

### ✅ Core Pages (Completed)

- **✅ Homepage** (`/`): Complete landing page with hero section, service overview, featured vehicles
- **✅ Services Page** (`/servicos`): Comprehensive service descriptions, process workflow, CTA sections
- **✅ Fleet Page** (`/frota`): Vehicle showcase with real-time data, modal integration, responsive grid
- **✅ Terms Page** (`/termos`): Complete terms and conditions with structured content
- **✅ Contact Page** (`/contacto`): Full-featured contact form, business info, Google Maps integration
- **✅ Booking Page** (`/reservas`): Multi-step booking system with complete workflow

### ✅ Booking System (Completed)

- **Multi-step Form**: 4-step wizard (vehicle selection, dates, customer details, confirmation)
- **State Management**: Custom `useBookingForm` hook with centralized state
- **Form Components**: Modular components for each step with validation
- **Date Validation**: Conflict detection with existing bookings
- **Progress Indicator**: Visual step progress with completion tracking
- **Success Handling**: Completion state with new booking option
- **Portuguese Localization**: All text and error messages in Portuguese

### ✅ Vehicle Management (Completed)

- **Vehicle Display**: Grid layouts with responsive design
- **Vehicle Cards**: Reusable components with image optimization
- **Vehicle Modals**: Detailed view with booking integration
- **Real-time Data**: Live fetching from Supabase with error handling
- **Image Optimization**: Next.js Image component with multiple source support

### ✅ Admin Panel (Completed)

- **✅ Booking Management**: Complete admin dashboard for booking management
  - **BookingsPage**: Main admin interface with filtering, status updates, dual view system
  - **BookingList**: List view with status filter tabs, quick actions, booking cards
  - **BookingDetail**: Detailed view with comprehensive booking information, status management, admin notes
  - **Status Management**: Portuguese status system (pendente, confirmado, cancelado) with transition logic
  - **Real-time Updates**: Live data fetching with optimistic updates for better UX

- **✅ Email Template System**: Comprehensive email management system
  - **EmailTemplateManager**: Main interface for template CRUD operations with search functionality
  - **EmailTemplateEditor**: Rich editor with 13 predefined variables, live preview, click-to-insert functionality
  - **Template Types**: Support for booking confirmation, cancellation, contact response, admin notifications
  - **Variable System**: Dynamic content with customer, booking, vehicle, and company variables
  - **Email Service**: High-level API for sending templated emails via Supabase Edge Functions
  - **Default Templates**: Portuguese templates created via setup script for immediate functionality
  - **Resend Integration**: Email delivery via Resend API with proper error handling

- **🚧 Authentication Integration**: Supabase Auth integration (planned)
- **🚧 Vehicle Management Interface**: Admin vehicle CRUD operations (planned)
- **🚧 SEO Management Tools**: Meta tag and content management (planned)

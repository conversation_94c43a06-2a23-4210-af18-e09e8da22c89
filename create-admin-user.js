// <PERSON>ript to create admin user for testing
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase credentials in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createAdminUser() {
  const adminEmail = '<EMAIL>';
  const adminPassword = 'admin123';

  console.log('🔧 Creating admin user...');
  console.log('Email:', adminEmail);
  console.log('Password:', adminPassword);

  try {
    // First, try to sign up the user
    const { data, error } = await supabase.auth.signUp({
      email: adminEmail,
      password: adminPassword,
      options: {
        data: {
          role: 'admin',
        },
      },
    });

    if (error) {
      console.error('❌ Error creating user:', error.message);

      // If user already exists, try to sign in
      if (error.message.includes('already registered')) {
        console.log('ℹ️  User already exists, trying to sign in...');

        const { data: signInData, error: signInError } =
          await supabase.auth.signInWithPassword({
            email: adminEmail,
            password: adminPassword,
          });

        if (signInError) {
          console.error('❌ Error signing in:', signInError.message);
          return;
        }

        console.log('✅ Successfully signed in existing user');
        console.log('User ID:', signInData.user?.id);
        console.log('User metadata:', signInData.user?.user_metadata);

        // Sign out after testing
        await supabase.auth.signOut();
        return;
      }
      return;
    }

    if (data.user) {
      console.log('✅ Admin user created successfully!');
      console.log('User ID:', data.user.id);
      console.log('Email:', data.user.email);
      console.log('User metadata:', data.user.user_metadata);

      // Sign out after creation
      await supabase.auth.signOut();

      console.log('\n🎉 Admin user is ready!');
      console.log('You can now login at: http://localhost:3000/admin/login');
      console.log('Email:', adminEmail);
      console.log('Password:', adminPassword);
    } else {
      console.log('⚠️  User created but confirmation may be required');
    }
  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

createAdminUser();

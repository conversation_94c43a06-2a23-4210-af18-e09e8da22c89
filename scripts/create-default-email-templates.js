const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const defaultTemplates = [
  {
    name: 'Confirmação de Reserva - Padrão',
    subject: 'Confirmação da sua reserva - {{vehicle_name}}',
    body: `Caro(a) {{customer_name}},

É com grande prazer que confirmamos a sua reserva do nosso veículo vintage {{vehicle_name}} ({{vehicle_year}}).

Detalhes da Reserva:
- ID da Reserva: {{booking_id}}
- Veículo: {{vehicle_name}} ({{vehicle_year}})
- Data de Início: {{start_date}}
- Data de Fim: {{end_date}}
- Preço Total: {{total_price}}

{{admin_notes}}

Próximos Passos:
1. Entraremos em contacto consigo 24-48 horas antes da data de início para coordenar a entrega/recolha
2. Por favor, tenha o seu documento de identificação disponível no momento da entrega
3. Certifique-se de que tem um condutor com carta de condução válida

Para qualquer questão ou alteração, não hesite em contactar-nos:
- Telefone: {{company_phone}}
- Email: {{company_email}}

Obrigado por escolher a {{company_name}} para o seu evento especial!

Com os melhores cumprimentos,
Equipa {{company_name}}`,
    template_type: 'booking_confirmation',
    is_active: true,
  },
  {
    name: 'Cancelamento de Reserva - Padrão',
    subject: 'Cancelamento da reserva - {{vehicle_name}}',
    body: `Caro(a) {{customer_name}},

Lamentamos informar que a sua reserva foi cancelada conforme solicitado.

Detalhes da Reserva Cancelada:
- ID da Reserva: {{booking_id}}
- Veículo: {{vehicle_name}} ({{vehicle_year}})
- Data de Início: {{start_date}}
- Data de Fim: {{end_date}}

{{admin_notes}}

Se este cancelamento não foi solicitado por si ou se tem alguma questão, por favor contacte-nos imediatamente:
- Telefone: {{company_phone}}
- Email: {{company_email}}

Esperamos poder servi-lo numa próxima oportunidade.

Com os melhores cumprimentos,
Equipa {{company_name}}`,
    template_type: 'booking_cancellation',
    is_active: true,
  },
  {
    name: 'Resposta a Contacto - Padrão',
    subject: 'Obrigado pelo seu contacto - {{company_name}}',
    body: `Caro(a) {{customer_name}},

Obrigado por nos contactar através do nosso website.

Recebemos a sua mensagem e entraremos em contacto consigo no prazo de 24 horas durante os dias úteis.

Se a sua questão for urgente, pode contactar-nos diretamente:
- Telefone: {{company_phone}}
- Email: {{company_email}}

Agradecemos o seu interesse nos nossos serviços de aluguer de veículos vintage.

Com os melhores cumprimentos,
Equipa {{company_name}}`,
    template_type: 'contact_response',
    is_active: true,
  },
];

async function createDefaultTemplates() {
  console.log('Creating default email templates...');

  try {
    // Check if templates already exist
    const { data: existingTemplates, error: checkError } = await supabase
      .from('email_templates')
      .select('template_type')
      .in('template_type', [
        'booking_confirmation',
        'booking_cancellation',
        'contact_response',
      ]);

    if (checkError) {
      console.error('Error checking existing templates:', checkError);
      return;
    }

    const existingTypes = existingTemplates?.map((t) => t.template_type) || [];
    const templatesToCreate = defaultTemplates.filter(
      (template) => !existingTypes.includes(template.template_type)
    );

    if (templatesToCreate.length === 0) {
      console.log('Default templates already exist. Skipping creation.');
      return;
    }

    const { data, error } = await supabase
      .from('email_templates')
      .insert(templatesToCreate)
      .select();

    if (error) {
      console.error('Error creating templates:', error);
      return;
    }

    console.log(`Successfully created ${data?.length || 0} email templates:`);
    data?.forEach((template) => {
      console.log(`- ${template.name} (${template.template_type})`);
    });
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the script
createDefaultTemplates()
  .then(() => {
    console.log('Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });

'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Booking, Vehicle, BookingStatus } from '@/types';
import {
  sendBookingConfirmationEmail,
  sendBookingCancellationEmail,
} from '@/lib/emailService';
import AdminLayout from '@/components/admin/AdminLayout';
import BookingList from '@/components/admin/BookingList';
import BookingDetail from '@/components/admin/BookingDetail';

interface BookingWithVehicle extends Booking {
  vehicle?: Vehicle;
}

export default function BookingsPage() {
  const [bookings, setBookings] = useState<BookingWithVehicle[]>([]);
  const [filteredBookings, setFilteredBookings] = useState<
    BookingWithVehicle[]
  >([]);
  const [selectedBooking, setSelectedBooking] =
    useState<BookingWithVehicle | null>(null);
  const [statusFilter, setStatusFilter] = useState<BookingStatus | 'all'>(
    'all'
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadBookings();
  }, []);

  useEffect(() => {
    filterBookings();
  }, [bookings, statusFilter]);

  const loadBookings = async () => {
    if (!supabase) {
      setError('Supabase não está configurado');
      setIsLoading(false);
      return;
    }

    try {
      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select(
          `
          *,
          vehicles (
            id,
            name,
            year,
            photo_url,
            description,
            price
          )
        `
        )
        .order('created_at', { ascending: false });

      if (bookingsError) {
        console.error('Error loading bookings:', bookingsError);
        setError('Erro ao carregar reservas');
        return;
      }

      const bookingsWithVehicles: BookingWithVehicle[] =
        bookingsData?.map((booking) => ({
          ...booking,
          vehicle: booking.vehicles as Vehicle,
        })) || [];

      setBookings(bookingsWithVehicles);
    } catch (error) {
      console.error('Unexpected error loading bookings:', error);
      setError('Erro inesperado ao carregar reservas');
    } finally {
      setIsLoading(false);
    }
  };

  const filterBookings = () => {
    if (statusFilter === 'all') {
      setFilteredBookings(bookings);
    } else {
      setFilteredBookings(
        bookings.filter((booking) => booking.status === statusFilter)
      );
    }
  };

  const handleStatusUpdate = async (
    bookingId: string,
    newStatus: BookingStatus,
    adminNotes?: string,
    sendEmail: boolean = true
  ) => {
    if (!supabase) return;

    try {
      const updateData: any = {
        status: newStatus,
        updated_at: new Date().toISOString(),
      };

      if (adminNotes !== undefined) {
        updateData.admin_notes = adminNotes;
      }

      const { error } = await supabase
        .from('bookings')
        .update(updateData)
        .eq('id', bookingId);

      if (error) {
        console.error('Error updating booking status:', error);
        setError('Erro ao atualizar status da reserva');
        return;
      }

      // Find the booking to get customer email
      const booking = bookings.find((b) => b.id === bookingId);

      // Send email notification if requested and booking found
      if (sendEmail && booking) {
        try {
          let emailResult;

          if (newStatus === 'confirmado') {
            emailResult = await sendBookingConfirmationEmail(
              bookingId,
              booking.customer_email
            );
          } else if (newStatus === 'cancelado') {
            emailResult = await sendBookingCancellationEmail(
              bookingId,
              booking.customer_email
            );
          }

          if (emailResult && !emailResult.success) {
            console.error('Error sending email:', emailResult.error);
            // Don't fail the status update if email fails, just log it
            setError(
              `Status atualizado, mas erro ao enviar email: ${emailResult.error}`
            );
          }
        } catch (emailError) {
          console.error('Error sending email:', emailError);
          setError(
            'Status atualizado, mas erro ao enviar email de notificação'
          );
        }
      }

      // Update local state
      setBookings((prevBookings) =>
        prevBookings.map((booking) =>
          booking.id === bookingId
            ? {
                ...booking,
                status: newStatus,
                admin_notes: adminNotes || booking.admin_notes,
              }
            : booking
        )
      );

      // Update selected booking if it's the one being updated
      if (selectedBooking?.id === bookingId) {
        setSelectedBooking((prev) =>
          prev
            ? {
                ...prev,
                status: newStatus,
                admin_notes: adminNotes || prev.admin_notes,
              }
            : null
        );
      }
    } catch (error) {
      console.error('Unexpected error updating booking:', error);
      setError('Erro inesperado ao atualizar reserva');
    }
  };

  const handleBookingSelect = (booking: BookingWithVehicle) => {
    setSelectedBooking(booking);
  };

  const handleBackToList = () => {
    setSelectedBooking(null);
  };

  const getStatusCounts = () => {
    return {
      all: bookings.length,
      pendente: bookings.filter((b) => b.status === 'pendente').length,
      confirmado: bookings.filter((b) => b.status === 'confirmado').length,
      cancelado: bookings.filter((b) => b.status === 'cancelado').length,
    };
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-black"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-700">{error}</p>
          <button
            onClick={loadBookings}
            className="mt-2 text-red-600 hover:text-red-800 underline"
          >
            Tentar novamente
          </button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {selectedBooking ? (
          <BookingDetail
            booking={selectedBooking}
            onBack={handleBackToList}
            onStatusUpdate={handleStatusUpdate}
          />
        ) : (
          <BookingList
            bookings={filteredBookings}
            statusFilter={statusFilter}
            statusCounts={getStatusCounts()}
            onStatusFilterChange={setStatusFilter}
            onBookingSelect={handleBookingSelect}
            onStatusUpdate={handleStatusUpdate}
          />
        )}
      </div>
    </AdminLayout>
  );
}

import { Metadata } from 'next';
import FadeIn from '@/components/animations/FadeIn';
import {
  ScrollReveal,
  TextReveal,
  StaggeredCard,
  HoverCard,
  AnimatedButton,
} from '@/components/animations';

export const metadata: Metadata = {
  title: 'Serviços - Vintage Marketing Portugal',
  description:
    'Descubra os nossos serviços de aluguer de veículos vintage para eventos corporativos, sessões fotográficas e campanhas de marketing em Portugal.',
  keywords:
    'serviços aluguer veículos vintage, eventos corporativos Portugal, sessões fotográficas vintage, campanhas marketing, aluguer carros vintage',
};

export default function ServicosPage() {
  return (
    <div className="min-h-screen bg-background-cream">
      {/* Hero Section */}
      <section className="section-alternate">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <FadeIn className="font-heading text-hero font-hero text-primary-black tracking-hero leading-hero mb-6">
              Os Nossos Serviços
            </FadeIn>
            <FadeIn
              delay={0.2}
              className="text-body text-primary-mediumGray leading-body mb-6 max-w-3xl mx-auto text-xl"
            >
              Oferecemos soluções completas de aluguer de veículos vintage para
              transformar os seus eventos de marketing em experiências
              memoráveis e autênticas. Todos os nossos veículos são
              disponibilizados exclusivamente para exposição e sessões
              fotográficas, sempre acompanhados por motorista profissional.
            </FadeIn>
            <FadeIn
              delay={0.4}
              className="inline-flex items-center bg-accent-vintage text-white px-6 py-3 rounded-full text-sm font-semibold shadow-lg"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
              Todos os nossos veículos incluem motorista profissional
            </FadeIn>
          </div>
        </div>
      </section>

      {/* Main Services */}
      <section className="section-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
            {/* Service 1 - Corporate Events */}
            <StaggeredCard index={0}>
              <HoverCard className="card-animated p-8 text-center">
                <div className="w-16 h-16 bg-accent-vintage text-white rounded-full flex items-center justify-center mb-6 mx-auto">
                  <svg
                    className="w-8 h-8"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"
                    />
                  </svg>
                </div>
                <h2 className="font-heading text-section font-section text-primary-black mb-4">
                  Eventos Corporativos
                </h2>
                <p className="text-body text-primary-mediumGray leading-body mb-6">
                  Impressione clientes, parceiros e colaboradores com a presença
                  única dos nossos veículos vintage em lançamentos de produtos,
                  conferências, feiras comerciais e eventos empresariais.
                </p>
                <div className="flex justify-center mb-6">
                  <ul className="space-y-2 text-body text-primary-mediumGray max-w-md text-left">
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-primary-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      Lançamentos de produtos e campanhas
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-primary-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      Conferências e seminários empresariais
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-primary-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      Feiras comerciais e exposições
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-primary-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      Eventos de networking e celebrações
                    </li>
                  </ul>
                </div>
              </HoverCard>
            </StaggeredCard>

            {/* Service 2 - Photo Shoots */}
            <StaggeredCard index={1}>
              <HoverCard className="card-animated p-8 text-center">
                <div className="w-16 h-16 bg-accent-vintage text-white rounded-full flex items-center justify-center mb-6 mx-auto">
                  <svg
                    className="w-8 h-8"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                </div>
                <h2 className="font-heading text-section font-section text-primary-black mb-4">
                  Sessões Fotográficas
                </h2>
                <p className="text-body text-primary-mediumGray leading-body mb-6">
                  Crie campanhas publicitárias memoráveis e conteúdo visual
                  impactante com o charme autêntico dos nossos veículos vintage
                  como cenário perfeito para as suas produções.
                </p>
                <div className="flex justify-center mb-6">
                  <ul className="space-y-2 text-body text-primary-mediumGray max-w-md text-left">
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-primary-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      Campanhas publicitárias e comerciais
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-primary-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      Sessões de moda e lifestyle
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-primary-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      Conteúdo para redes sociais
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-primary-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      Produções cinematográficas e vídeo
                    </li>
                  </ul>
                </div>
              </HoverCard>
            </StaggeredCard>
          </div>

          {/* Driver Experience Section */}
          <div className="mb-16">
            <div className="text-center mb-12">
              <TextReveal className="font-heading text-section font-section text-primary-black mb-6">
                Experiências com Motorista
              </TextReveal>
              <TextReveal
                delay={0.2}
                className="text-body text-primary-mediumGray leading-body max-w-3xl mx-auto"
              >
                Todos os nossos veículos vintage são disponibilizados com
                motorista profissional experiente, garantindo segurança,
                preservação do património histórico e uma experiência autêntica.
              </TextReveal>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              <StaggeredCard index={0}>
                <HoverCard className="card-animated p-6">
                  <div className="flex items-start mb-4">
                    <div className="w-12 h-12 bg-accent-vintage text-white rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                      <svg
                        className="w-6 h-6"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                        />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-heading text-card font-card text-primary-black mb-2">
                        Exposições e Eventos
                      </h3>
                      <p className="text-body text-primary-mediumGray leading-body">
                        Perfeito para exposições, lançamentos de produtos e
                        eventos corporativos onde o veículo serve como peça
                        central de atração.
                      </p>
                    </div>
                  </div>
                </HoverCard>
              </StaggeredCard>

              <StaggeredCard index={1}>
                <HoverCard className="card-animated p-6">
                  <div className="flex items-start mb-4">
                    <div className="w-12 h-12 bg-accent-vintage text-white rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                      <svg
                        className="w-6 h-6"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-heading text-card font-card text-primary-black mb-2">
                        Sessões Fotográficas Profissionais
                      </h3>
                      <p className="text-body text-primary-mediumGray leading-body">
                        Ideal para campanhas publicitárias e produções onde o
                        motorista posiciona o veículo conforme as necessidades
                        da produção.
                      </p>
                    </div>
                  </div>
                </HoverCard>
              </StaggeredCard>
            </div>

            <div className="bg-background-beige text-primary-black p-8 rounded-xl border border-primary-lightGray/20">
              <div className="text-center">
                <h3 className="font-heading text-card font-card mb-4">
                  Porquê Motorista Profissional?
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                  <div className="flex flex-col items-center">
                    <svg
                      className="w-8 h-8 text-accent-vintage mb-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="font-semibold text-primary-black">
                      Segurança
                    </span>
                    <span className="text-primary-mediumGray text-center">
                      Proteção total do veículo histórico
                    </span>
                  </div>
                  <div className="flex flex-col items-center">
                    <svg
                      className="w-8 h-8 text-accent-vintage mb-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 14l9-5-9-5-9 5 9 5z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"
                      />
                    </svg>
                    <span className="font-semibold text-primary-black">
                      Experiência
                    </span>
                    <span className="text-primary-mediumGray text-center">
                      Conhecimento especializado dos veículos
                    </span>
                  </div>
                  <div className="flex flex-col items-center">
                    <svg
                      className="w-8 h-8 text-accent-vintage mb-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="font-semibold text-primary-black">
                      Autenticidade
                    </span>
                    <span className="text-primary-mediumGray text-center">
                      Preservação do património vintage
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Services */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <StaggeredCard index={0}>
              <HoverCard className="card-animated p-6 text-center">
                <div className="w-12 h-12 bg-accent-vintage text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                    />
                  </svg>
                </div>
                <h3 className="font-heading text-card font-card text-primary-black mb-3">
                  Eventos Especiais
                </h3>
                <p className="text-body text-primary-mediumGray leading-body">
                  Casamentos, aniversários, festivais e celebrações únicas com
                  elegância vintage.
                </p>
              </HoverCard>
            </StaggeredCard>

            <StaggeredCard index={1}>
              <HoverCard className="card-animated p-6 text-center">
                <div className="w-12 h-12 bg-accent-vintage text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                    />
                  </svg>
                </div>
                <h3 className="font-heading text-card font-card text-primary-black mb-3">
                  Consultoria Criativa
                </h3>
                <p className="text-body text-primary-mediumGray leading-body">
                  Apoio na conceção e planeamento de campanhas que maximizam o
                  impacto dos veículos vintage.
                </p>
              </HoverCard>
            </StaggeredCard>

            <StaggeredCard index={2}>
              <HoverCard className="card-animated p-6 text-center">
                <div className="w-12 h-12 bg-accent-vintage text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h3 className="font-heading text-card font-card text-primary-black mb-3">
                  Flexibilidade Total
                </h3>
                <p className="text-body text-primary-mediumGray leading-body">
                  Aluguer por horas, dias ou períodos prolongados, adaptado às
                  suas necessidades específicas.
                </p>
              </HoverCard>
            </StaggeredCard>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="section-alternate">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <TextReveal className="font-heading text-section font-section text-primary-black mb-6">
              Como Funciona
            </TextReveal>
            <TextReveal
              delay={0.2}
              className="text-body text-primary-mediumGray leading-body max-w-3xl mx-auto"
            >
              Um processo simples e transparente para garantir que o seu evento
              seja perfeito em todos os detalhes.
            </TextReveal>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <ScrollReveal delay={0.1}>
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-white text-primary-black rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-xl">
                  1
                </div>
                <h3 className="font-heading text-card font-card text-primary-black mb-3">
                  Consulta
                </h3>
                <p className="text-body-secondary text-primary-mediumGray">
                  Contacte-nos para discutir as suas necessidades e objetivos do
                  evento.
                </p>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.2}>
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-white text-primary-black rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-xl">
                  2
                </div>
                <h3 className="font-heading text-card font-card text-primary-black mb-3">
                  Seleção
                </h3>
                <p className="text-body-secondary text-primary-mediumGray">
                  Escolha o veículo vintage perfeito da nossa frota
                  cuidadosamente selecionada.
                </p>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.3}>
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-white text-primary-black rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-xl">
                  3
                </div>
                <h3 className="font-heading text-card font-card text-primary-black mb-3">
                  Reserva
                </h3>
                <p className="text-body-secondary text-primary-mediumGray">
                  Confirme as datas e detalhes do seu evento com a nossa equipa
                  especializada.
                </p>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.4}>
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-white text-primary-black rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-xl">
                  4
                </div>
                <h3 className="font-heading text-card font-card text-primary-black mb-3">
                  Evento
                </h3>
                <p className="text-body-secondary text-primary-mediumGray">
                  Desfrute do seu evento memorável com o apoio completo da nossa
                  equipa.
                </p>
              </div>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="section-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <TextReveal className="font-heading text-section font-section text-primary-black mb-6">
            Pronto para Começar?
          </TextReveal>
          <TextReveal
            delay={0.2}
            className="text-body text-primary-mediumGray leading-body mb-8 max-w-2xl mx-auto"
          >
            Entre em contacto connosco hoje e descubra como podemos tornar o seu
            próximo evento verdadeiramente inesquecível com os nossos veículos
            vintage.
          </TextReveal>
          <ScrollReveal delay={0.4}>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <AnimatedButton
                href="/frota"
                variant="primary"
                className="flex items-center justify-center h-12"
                enableGlow={true}
                glowColor="rgba(0, 0, 0, 0.15)"
              >
                Ver a Nossa Frota
              </AnimatedButton>
              <AnimatedButton
                href="/contacto"
                variant="secondary"
                className="flex items-center justify-center h-12"
                enableGlow={true}
                glowColor="rgba(0, 0, 0, 0.15)"
              >
                Contacte-nos Agora
                <svg
                  className="w-4 h-4 ml-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </AnimatedButton>
            </div>
          </ScrollReveal>
        </div>
      </section>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { BookingStatus, EmailTemplate } from '@/types';
import { sendTemplateEmail, getEmailTemplates } from '@/lib/emailService';
import { FadeIn, SlideIn } from '@/components/animations';

interface BookingWithVehicle {
  id: string;
  customer_name: string;
  customer_email: string;
  customer_phone?: string | null;
  start_date: string;
  end_date: string;
  status: string;
  total_price?: number | null;
  notes?: string | null;
  admin_notes?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
  vehicle?: {
    id: string;
    name: string;
    year: number;
    photo_url?: string | null;
    description?: string | null;
    price?: number | null;
  };
}

interface BookingDetailProps {
  booking: BookingWithVehicle;
  onBack: () => void;
  onStatusUpdate: (
    bookingId: string,
    newStatus: BookingStatus,
    adminNotes?: string,
    sendEmail?: boolean
  ) => void;
}

export default function BookingDetail({
  booking,
  onBack,
  onStatusUpdate,
}: BookingDetailProps) {
  const [adminNotes, setAdminNotes] = useState(booking.admin_notes || '');
  const [isUpdating, setIsUpdating] = useState(false);
  const [showStatusChange, setShowStatusChange] = useState(false);
  const [showEmailOptions, setShowEmailOptions] = useState(false);
  const [emailTemplates, setEmailTemplates] = useState<EmailTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [pendingStatusChange, setPendingStatusChange] =
    useState<BookingStatus | null>(null);
  const [sendEmailWithStatusChange, setSendEmailWithStatusChange] =
    useState(true);

  useEffect(() => {
    loadEmailTemplates();
  }, []);

  const loadEmailTemplates = async () => {
    const { data, error } = await getEmailTemplates();
    if (data && !error) {
      setEmailTemplates(data);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-PT', {
      weekday: 'long',
      day: '2-digit',
      month: 'long',
      year: 'numeric',
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusBadge = (status: string) => {
    const baseClasses =
      'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium';

    switch (status) {
      case 'pendente':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'confirmado':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'cancelado':
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pendente':
        return 'Pendente';
      case 'confirmado':
        return 'Confirmado';
      case 'cancelado':
        return 'Cancelado';
      default:
        return status;
    }
  };

  const handleStatusUpdate = async (newStatus: BookingStatus) => {
    // If email should be sent, show email options first
    if (
      sendEmailWithStatusChange &&
      (newStatus === 'confirmado' || newStatus === 'cancelado')
    ) {
      setPendingStatusChange(newStatus);
      setShowEmailOptions(true);
      setShowStatusChange(false);
      return;
    }

    // Otherwise, proceed with status update
    setIsUpdating(true);
    try {
      await onStatusUpdate(
        booking.id,
        newStatus,
        adminNotes,
        sendEmailWithStatusChange
      );
      setShowStatusChange(false);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleConfirmStatusWithEmail = async () => {
    if (!pendingStatusChange) return;

    setIsUpdating(true);
    try {
      // Send custom email if template selected
      if (selectedTemplate) {
        const emailResult = await sendTemplateEmail({
          to: booking.customer_email,
          template_id: selectedTemplate,
          booking_id: booking.id,
        });

        if (!emailResult.success) {
          alert(`Erro ao enviar email: ${emailResult.error}`);
          return;
        }
      }

      // Update status without sending automatic email (since we sent custom one)
      await onStatusUpdate(booking.id, pendingStatusChange, adminNotes, false);

      setShowEmailOptions(false);
      setPendingStatusChange(null);
      setSelectedTemplate('');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleSkipEmail = async () => {
    if (!pendingStatusChange) return;

    setIsUpdating(true);
    try {
      await onStatusUpdate(booking.id, pendingStatusChange, adminNotes, false);
      setShowEmailOptions(false);
      setPendingStatusChange(null);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleNotesUpdate = async () => {
    setIsUpdating(true);
    try {
      await onStatusUpdate(
        booking.id,
        booking.status as BookingStatus,
        adminNotes
      );
    } finally {
      setIsUpdating(false);
    }
  };

  const calculateDuration = () => {
    const start = new Date(booking.start_date);
    const end = new Date(booking.end_date);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getAvailableStatusChanges = () => {
    const current = booking.status;
    const options: { status: BookingStatus; label: string; color: string }[] =
      [];

    if (current === 'pendente') {
      options.push(
        {
          status: 'confirmado',
          label: 'Confirmar Reserva',
          color: 'bg-green-600 hover:bg-green-700',
        },
        {
          status: 'cancelado',
          label: 'Cancelar Reserva',
          color: 'bg-red-600 hover:bg-red-700',
        }
      );
    } else if (current === 'confirmado') {
      options.push({
        status: 'cancelado',
        label: 'Cancelar Reserva',
        color: 'bg-red-600 hover:bg-red-700',
      });
    } else if (current === 'cancelado') {
      options.push({
        status: 'confirmado',
        label: 'Reativar Reserva',
        color: 'bg-green-600 hover:bg-green-700',
      });
    }

    return options;
  };

  return (
    <FadeIn>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <button
            onClick={onBack}
            className="flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
          >
            ← Voltar à lista de reservas
          </button>

          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">
                Reserva de {booking.customer_name}
              </h1>
              <p className="text-sm text-gray-500 mt-1">ID: {booking.id}</p>
            </div>
            <div className="flex items-center space-x-3">
              <span className={getStatusBadge(booking.status)}>
                {getStatusText(booking.status)}
              </span>
              <button
                onClick={() => setShowStatusChange(!showStatusChange)}
                className="px-4 py-2 bg-primary-black text-white text-sm font-medium rounded-md hover:bg-primary-mediumGray focus:outline-none focus:ring-2 focus:ring-primary-black"
              >
                Alterar Status
              </button>
            </div>
          </div>
        </div>

        {/* Status Change Panel */}
        {showStatusChange && (
          <SlideIn direction="down">
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                Alterar Status da Reserva
              </h3>

              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={sendEmailWithStatusChange}
                    onChange={(e) =>
                      setSendEmailWithStatusChange(e.target.checked)
                    }
                    className="h-4 w-4 text-primary-black focus:ring-primary-black border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    Enviar email de notificação ao cliente
                  </span>
                </label>
              </div>

              <div className="flex space-x-3">
                {getAvailableStatusChanges().map((option) => (
                  <button
                    key={option.status}
                    onClick={() => handleStatusUpdate(option.status)}
                    disabled={isUpdating}
                    className={`px-4 py-2 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 disabled:opacity-50 ${option.color}`}
                  >
                    {isUpdating ? 'A atualizar...' : option.label}
                  </button>
                ))}
                <button
                  onClick={() => setShowStatusChange(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Cancelar
                </button>
              </div>
            </div>
          </SlideIn>
        )}

        {/* Email Options Panel */}
        {showEmailOptions && pendingStatusChange && (
          <SlideIn direction="down">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                Enviar Email de{' '}
                {pendingStatusChange === 'confirmado'
                  ? 'Confirmação'
                  : 'Cancelamento'}
              </h3>

              <div className="mb-4">
                <label
                  htmlFor="email-template"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Selecionar Template de Email (opcional)
                </label>
                <select
                  id="email-template"
                  value={selectedTemplate}
                  onChange={(e) => setSelectedTemplate(e.target.value)}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-black focus:ring-primary-black"
                >
                  <option value="">Usar template padrão</option>
                  {emailTemplates
                    .filter(
                      (template) =>
                        template.template_type ===
                        `booking_${pendingStatusChange === 'confirmado' ? 'confirmation' : 'cancellation'}`
                    )
                    .map((template) => (
                      <option key={template.id} value={template.id}>
                        {template.name}
                      </option>
                    ))}
                </select>
              </div>

              <div className="text-sm text-gray-600 mb-4">
                <p>
                  <strong>Para:</strong> {booking.customer_email}
                </p>
                <p>
                  <strong>Cliente:</strong> {booking.customer_name}
                </p>
                <p>
                  <strong>Veículo:</strong> {booking.vehicle?.name}
                </p>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleConfirmStatusWithEmail}
                  disabled={isUpdating}
                  className="px-4 py-2 bg-primary-black text-white text-sm font-medium rounded-md hover:bg-primary-mediumGray focus:outline-none focus:ring-2 focus:ring-primary-black disabled:opacity-50"
                >
                  {isUpdating ? 'A processar...' : 'Confirmar e Enviar Email'}
                </button>
                <button
                  onClick={handleSkipEmail}
                  disabled={isUpdating}
                  className="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50"
                >
                  {isUpdating ? 'A processar...' : 'Alterar Status Sem Email'}
                </button>
                <button
                  onClick={() => {
                    setShowEmailOptions(false);
                    setPendingStatusChange(null);
                    setSelectedTemplate('');
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Cancelar
                </button>
              </div>
            </div>
          </SlideIn>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Customer Information */}
            <SlideIn direction="left">
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Informações do Cliente
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Nome
                    </label>
                    <p className="mt-1 text-sm text-gray-900">
                      {booking.customer_name}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Email
                    </label>
                    <p className="mt-1 text-sm text-gray-900">
                      <a
                        href={`mailto:${booking.customer_email}`}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {booking.customer_email}
                      </a>
                    </p>
                  </div>
                  {booking.customer_phone && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Telefone
                      </label>
                      <p className="mt-1 text-sm text-gray-900">
                        <a
                          href={`tel:${booking.customer_phone}`}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          {booking.customer_phone}
                        </a>
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </SlideIn>

            {/* Booking Details */}
            <SlideIn direction="left" delay={0.1}>
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Detalhes da Reserva
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Data de Início
                    </label>
                    <p className="mt-1 text-sm text-gray-900">
                      {formatDate(booking.start_date)}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Data de Fim
                    </label>
                    <p className="mt-1 text-sm text-gray-900">
                      {formatDate(booking.end_date)}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Duração
                    </label>
                    <p className="mt-1 text-sm text-gray-900">
                      {calculateDuration()}{' '}
                      {calculateDuration() === 1 ? 'dia' : 'dias'}
                    </p>
                  </div>
                  {booking.total_price && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Preço Total
                      </label>
                      <p className="mt-1 text-lg font-semibold text-green-600">
                        €{booking.total_price.toFixed(2)}
                      </p>
                    </div>
                  )}
                </div>

                {booking.notes && (
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700">
                      Notas do Cliente
                    </label>
                    <p className="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
                      {booking.notes}
                    </p>
                  </div>
                )}
              </div>
            </SlideIn>

            {/* Admin Notes */}
            <SlideIn direction="left" delay={0.2}>
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Notas Administrativas
                </h2>
                <div>
                  <textarea
                    value={adminNotes}
                    onChange={(e) => setAdminNotes(e.target.value)}
                    rows={4}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-black focus:border-transparent"
                    placeholder="Adicione notas internas sobre esta reserva..."
                  />
                  <div className="mt-3 flex justify-end">
                    <button
                      onClick={handleNotesUpdate}
                      disabled={
                        isUpdating || adminNotes === (booking.admin_notes || '')
                      }
                      className="px-4 py-2 bg-primary-black text-white text-sm font-medium rounded-md hover:bg-primary-mediumGray focus:outline-none focus:ring-2 focus:ring-primary-black disabled:opacity-50"
                    >
                      {isUpdating ? 'A guardar...' : 'Guardar Notas'}
                    </button>
                  </div>
                </div>
              </div>
            </SlideIn>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Vehicle Information */}
            <SlideIn direction="right">
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Veículo Reservado
                </h2>
                {booking.vehicle ? (
                  <div>
                    {booking.vehicle.photo_url && (
                      <img
                        src={booking.vehicle.photo_url}
                        alt={booking.vehicle.name}
                        className="w-full h-32 object-cover rounded-lg mb-3"
                      />
                    )}
                    <h3 className="font-medium text-gray-900">
                      {booking.vehicle.name}
                    </h3>
                    <p className="text-sm text-gray-500">
                      Ano: {booking.vehicle.year}
                    </p>
                    {booking.vehicle.description && (
                      <p className="text-sm text-gray-600 mt-2">
                        {booking.vehicle.description}
                      </p>
                    )}
                    {booking.vehicle.price && (
                      <p className="text-sm font-medium text-green-600 mt-2">
                        €{booking.vehicle.price}/dia
                      </p>
                    )}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">
                    Informações do veículo não disponíveis
                  </p>
                )}
              </div>
            </SlideIn>

            {/* Timeline */}
            <SlideIn direction="right" delay={0.1}>
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Histórico
                </h2>
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        Reserva criada
                      </p>
                      <p className="text-xs text-gray-500">
                        {booking.created_at
                          ? formatDateTime(booking.created_at)
                          : 'Data não disponível'}
                      </p>
                    </div>
                  </div>

                  {booking.updated_at &&
                    booking.updated_at !== booking.created_at && (
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            Última atualização
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatDateTime(booking.updated_at)}
                          </p>
                        </div>
                      </div>
                    )}
                </div>
              </div>
            </SlideIn>
          </div>
        </div>
      </div>
    </FadeIn>
  );
}

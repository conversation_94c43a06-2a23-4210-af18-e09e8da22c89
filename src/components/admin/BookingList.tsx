'use client';

import { useState } from 'react';
import { BookingStatus } from '@/types';
import { FadeIn, StaggerContainer } from '@/components/animations';

interface BookingWithVehicle {
  id: string;
  customer_name: string;
  customer_email: string;
  customer_phone?: string | null;
  start_date: string;
  end_date: string;
  status: string;
  total_price?: number | null;
  notes?: string | null;
  admin_notes?: string | null;
  created_at?: string | null;
  vehicle?: {
    id: string;
    name: string;
    year: number;
    photo_url?: string | null;
  };
}

interface BookingListProps {
  bookings: BookingWithVehicle[];
  statusFilter: BookingStatus | 'all';
  statusCounts: {
    all: number;
    pendente: number;
    confirmado: number;
    cancelado: number;
  };
  onStatusFilterChange: (status: BookingStatus | 'all') => void;
  onBookingSelect: (booking: BookingWithVehicle) => void;
  onStatusUpdate: (
    bookingId: string,
    newStatus: BookingStatus,
    adminNotes?: string
  ) => void;
}

export default function BookingList({
  bookings,
  statusFilter,
  statusCounts,
  onStatusFilterChange,
  onBookingSelect,
  onStatusUpdate,
}: BookingListProps) {
  const [updatingBookings, setUpdatingBookings] = useState<Set<string>>(
    new Set()
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusBadge = (status: string) => {
    const baseClasses =
      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';

    switch (status) {
      case 'pendente':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'confirmado':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'cancelado':
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pendente':
        return 'Pendente';
      case 'confirmado':
        return 'Confirmado';
      case 'cancelado':
        return 'Cancelado';
      default:
        return status;
    }
  };

  const handleQuickStatusUpdate = async (
    bookingId: string,
    newStatus: BookingStatus
  ) => {
    setUpdatingBookings((prev) => new Set(prev).add(bookingId));

    try {
      await onStatusUpdate(bookingId, newStatus);
    } finally {
      setUpdatingBookings((prev) => {
        const newSet = new Set(prev);
        newSet.delete(bookingId);
        return newSet;
      });
    }
  };

  const filterTabs = [
    { key: 'all' as const, label: 'Todas', count: statusCounts.all },
    {
      key: 'pendente' as const,
      label: 'Pendentes',
      count: statusCounts.pendente,
    },
    {
      key: 'confirmado' as const,
      label: 'Confirmadas',
      count: statusCounts.confirmado,
    },
    {
      key: 'cancelado' as const,
      label: 'Canceladas',
      count: statusCounts.cancelado,
    },
  ];

  return (
    <FadeIn>
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">
            Gestão de Reservas
          </h1>
          <p className="mt-2 text-sm text-gray-700">
            Gerir todas as reservas de veículos vintage
          </p>
        </div>
      </div>

      {/* Status Filter Tabs */}
      <div className="mt-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {filterTabs.map((tab) => (
              <button
                key={tab.key}
                onClick={() => onStatusFilterChange(tab.key)}
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  statusFilter === tab.key
                    ? 'border-primary-black text-primary-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                  {tab.count}
                </span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Bookings List */}
      <div className="mt-6">
        {bookings.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📅</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Nenhuma reserva encontrada
            </h3>
            <p className="text-gray-500">
              {statusFilter === 'all'
                ? 'Não há reservas no sistema.'
                : `Não há reservas com status "${getStatusText(statusFilter)}".`}
            </p>
          </div>
        ) : (
          <StaggerContainer className="space-y-4">
            {bookings.map((booking, index) => (
              <FadeIn key={booking.id} delay={index * 0.1}>
                <div className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                  <div className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-3">
                          <div className="flex-shrink-0">
                            {booking.vehicle?.photo_url ? (
                              <img
                                src={booking.vehicle.photo_url}
                                alt={booking.vehicle.name}
                                className="w-12 h-12 rounded-lg object-cover"
                              />
                            ) : (
                              <div className="w-12 h-12 bg-background-beige rounded-lg flex items-center justify-center">
                                <span className="text-accent-vintage font-medium text-lg">
                                  🚗
                                </span>
                              </div>
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-3">
                              <h3 className="text-lg font-medium text-gray-900">
                                {booking.customer_name}
                              </h3>
                              <span className={getStatusBadge(booking.status)}>
                                {getStatusText(booking.status)}
                              </span>
                            </div>
                            <p className="text-sm text-gray-500">
                              {booking.vehicle?.name} •{' '}
                              {formatDate(booking.start_date)} -{' '}
                              {formatDate(booking.end_date)}
                            </p>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-700">
                              Email:
                            </span>
                            <p className="text-gray-600">
                              {booking.customer_email}
                            </p>
                          </div>
                          {booking.customer_phone && (
                            <div>
                              <span className="font-medium text-gray-700">
                                Telefone:
                              </span>
                              <p className="text-gray-600">
                                {booking.customer_phone}
                              </p>
                            </div>
                          )}
                          <div>
                            <span className="font-medium text-gray-700">
                              Criado em:
                            </span>
                            <p className="text-gray-600">
                              {booking.created_at
                                ? formatDateTime(booking.created_at)
                                : 'N/A'}
                            </p>
                          </div>
                        </div>

                        {booking.total_price && (
                          <div className="mt-3">
                            <span className="font-medium text-gray-700">
                              Preço Total:
                            </span>
                            <span className="ml-2 text-lg font-semibold text-green-600">
                              €{booking.total_price.toFixed(2)}
                            </span>
                          </div>
                        )}

                        {booking.admin_notes && (
                          <div className="mt-3">
                            <span className="font-medium text-gray-700">
                              Notas Admin:
                            </span>
                            <p className="text-gray-600 bg-gray-50 p-2 rounded-md mt-1">
                              {booking.admin_notes}
                            </p>
                          </div>
                        )}
                      </div>

                      <div className="flex-shrink-0 ml-6">
                        <div className="flex flex-col space-y-2">
                          <button
                            onClick={() => onBookingSelect(booking)}
                            className="px-4 py-2 bg-primary-black text-white text-sm font-medium rounded-md hover:bg-primary-mediumGray focus:outline-none focus:ring-2 focus:ring-primary-black"
                          >
                            Ver Detalhes
                          </button>

                          {booking.status === 'pendente' && (
                            <>
                              <button
                                onClick={() =>
                                  handleQuickStatusUpdate(
                                    booking.id,
                                    'confirmado'
                                  )
                                }
                                disabled={updatingBookings.has(booking.id)}
                                className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
                              >
                                {updatingBookings.has(booking.id)
                                  ? 'A atualizar...'
                                  : 'Aprovar'}
                              </button>
                              <button
                                onClick={() =>
                                  handleQuickStatusUpdate(
                                    booking.id,
                                    'cancelado'
                                  )
                                }
                                disabled={updatingBookings.has(booking.id)}
                                className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
                              >
                                {updatingBookings.has(booking.id)
                                  ? 'A atualizar...'
                                  : 'Cancelar'}
                              </button>
                            </>
                          )}

                          {booking.status === 'confirmado' && (
                            <button
                              onClick={() =>
                                handleQuickStatusUpdate(booking.id, 'cancelado')
                              }
                              disabled={updatingBookings.has(booking.id)}
                              className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
                            >
                              {updatingBookings.has(booking.id)
                                ? 'A atualizar...'
                                : 'Cancelar'}
                            </button>
                          )}

                          {booking.status === 'cancelado' && (
                            <button
                              onClick={() =>
                                handleQuickStatusUpdate(
                                  booking.id,
                                  'confirmado'
                                )
                              }
                              disabled={updatingBookings.has(booking.id)}
                              className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
                            >
                              {updatingBookings.has(booking.id)
                                ? 'A atualizar...'
                                : 'Reativar'}
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </FadeIn>
            ))}
          </StaggerContainer>
        )}
      </div>
    </FadeIn>
  );
}

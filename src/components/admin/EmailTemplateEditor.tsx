'use client';

import { useState, useEffect } from 'react';
import {
  EmailTemplate,
  EmailTemplateInsert,
  EmailTemplateUpdate,
} from '@/types';
import { supabase } from '@/lib/supabase';

interface EmailTemplateEditorProps {
  template?: EmailTemplate;
  onSave: (template: EmailTemplate) => void;
  onCancel: () => void;
}

const TEMPLATE_VARIABLES = [
  { key: 'customer_name', label: 'Nome do Cliente', example: 'João Silva' },
  {
    key: 'customer_email',
    label: 'Email do Cliente',
    example: '<EMAIL>',
  },
  {
    key: 'customer_phone',
    label: 'Telefone do Cliente',
    example: '+351 912 345 678',
  },
  {
    key: 'vehicle_name',
    label: 'Nome do Veículo',
    example: 'Fleur de Lys Van',
  },
  { key: 'vehicle_year', label: 'Ano do Veículo', example: '1975' },
  {
    key: 'start_date',
    label: 'Data de Início',
    example: '15 de Janeiro de 2025',
  },
  { key: 'end_date', label: 'Data de Fim', example: '17 de Janeiro de 2025' },
  { key: 'total_price', label: 'Preço Total', example: '€450.00' },
  { key: 'booking_id', label: 'ID da Reserva', example: 'VMP-2025-001' },
  {
    key: 'admin_notes',
    label: 'Notas do Admin',
    example: 'Entrega no local do evento',
  },
  {
    key: 'company_name',
    label: 'Nome da Empresa',
    example: 'Vintage Marketing Portugal',
  },
  {
    key: 'company_phone',
    label: 'Telefone da Empresa',
    example: '+*********** 591',
  },
  {
    key: 'company_email',
    label: 'Email da Empresa',
    example: '<EMAIL>',
  },
];

export default function EmailTemplateEditor({
  template,
  onSave,
  onCancel,
}: EmailTemplateEditorProps) {
  const [formData, setFormData] = useState<EmailTemplateInsert>({
    name: '',
    subject: '',
    body: '',
    template_type: 'booking_confirmation',
    is_active: true,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewData, setPreviewData] = useState<Record<string, string>>({});

  useEffect(() => {
    if (template) {
      setFormData({
        name: template.name,
        subject: template.subject,
        body: template.body,
        template_type: template.template_type,
        is_active: template.is_active ?? true,
      });
    }

    // Set example data for preview
    const exampleData: Record<string, string> = {};
    TEMPLATE_VARIABLES.forEach((variable) => {
      exampleData[variable.key] = variable.example;
    });
    setPreviewData(exampleData);
  }, [template]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!supabase) return;

    setIsLoading(true);
    try {
      let result;

      if (template) {
        // Update existing template
        const { data, error } = await supabase
          .from('email_templates')
          .update({
            ...formData,
            updated_at: new Date().toISOString(),
          })
          .eq('id', template.id)
          .select()
          .single();

        result = { data, error };
      } else {
        // Create new template
        const { data, error } = await supabase
          .from('email_templates')
          .insert(formData)
          .select()
          .single();

        result = { data, error };
      }

      if (result.error) {
        console.error('Error saving template:', result.error);
        alert('Erro ao guardar template: ' + result.error.message);
        return;
      }

      if (result.data) {
        onSave(result.data);
      }
    } catch (error) {
      console.error('Error saving template:', error);
      alert('Erro ao guardar template');
    } finally {
      setIsLoading(false);
    }
  };

  const insertVariable = (variableKey: string) => {
    const textarea = document.getElementById(
      'template-body'
    ) as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const text = textarea.value;
      const before = text.substring(0, start);
      const after = text.substring(end, text.length);
      const newText = before + `{{${variableKey}}}` + after;

      setFormData((prev) => ({ ...prev, body: newText }));

      // Set cursor position after the inserted variable
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(
          start + variableKey.length + 4,
          start + variableKey.length + 4
        );
      }, 0);
    }
  };

  const renderPreview = (text: string) => {
    let preview = text;
    TEMPLATE_VARIABLES.forEach((variable) => {
      const regex = new RegExp(`{{${variable.key}}}`, 'g');
      preview = preview.replace(
        regex,
        previewData[variable.key] || `{{${variable.key}}}`
      );
    });
    return preview;
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white shadow-sm rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">
            {template ? 'Editar Template de Email' : 'Novo Template de Email'}
          </h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Form Fields */}
            <div className="lg:col-span-2 space-y-6">
              <div>
                <label
                  htmlFor="name"
                  className="block text-sm font-medium text-gray-700"
                >
                  Nome do Template
                </label>
                <input
                  type="text"
                  id="name"
                  required
                  value={formData.name}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, name: e.target.value }))
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-black focus:ring-primary-black"
                  placeholder="Ex: Confirmação de Reserva"
                />
              </div>

              <div>
                <label
                  htmlFor="template_type"
                  className="block text-sm font-medium text-gray-700"
                >
                  Tipo de Template
                </label>
                <select
                  id="template_type"
                  value={formData.template_type}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      template_type: e.target.value,
                    }))
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-black focus:ring-primary-black"
                >
                  <option value="booking_confirmation">
                    Confirmação de Reserva
                  </option>
                  <option value="booking_cancellation">
                    Cancelamento de Reserva
                  </option>
                  <option value="contact_response">Resposta a Contacto</option>
                  <option value="admin_notification">Notificação Admin</option>
                </select>
              </div>

              <div>
                <label
                  htmlFor="subject"
                  className="block text-sm font-medium text-gray-700"
                >
                  Assunto do Email
                </label>
                <input
                  type="text"
                  id="subject"
                  required
                  value={formData.subject}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      subject: e.target.value,
                    }))
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-black focus:ring-primary-black"
                  placeholder="Ex: Confirmação da sua reserva - {{vehicle_name}}"
                />
              </div>

              <div>
                <div className="flex justify-between items-center mb-2">
                  <label
                    htmlFor="template-body"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Corpo do Email
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowPreview(!showPreview)}
                    className="text-sm text-primary-black hover:text-primary-mediumGray"
                  >
                    {showPreview ? 'Editar' : 'Pré-visualizar'}
                  </button>
                </div>

                {showPreview ? (
                  <div className="border border-gray-300 rounded-md p-4 bg-gray-50 min-h-[300px]">
                    <div className="whitespace-pre-wrap text-sm">
                      {renderPreview(formData.body)}
                    </div>
                  </div>
                ) : (
                  <textarea
                    id="template-body"
                    required
                    rows={12}
                    value={formData.body}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, body: e.target.value }))
                    }
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-black focus:ring-primary-black"
                    placeholder="Escreva o conteúdo do email aqui. Use {{variável}} para inserir dados dinâmicos."
                  />
                )}
              </div>

              <div className="flex items-center">
                <input
                  id="is_active"
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      is_active: e.target.checked,
                    }))
                  }
                  className="h-4 w-4 text-primary-black focus:ring-primary-black border-gray-300 rounded"
                />
                <label
                  htmlFor="is_active"
                  className="ml-2 block text-sm text-gray-900"
                >
                  Template ativo
                </label>
              </div>
            </div>

            {/* Variables Panel */}
            <div className="lg:col-span-1">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-900 mb-3">
                  Variáveis Disponíveis
                </h3>
                <p className="text-xs text-gray-600 mb-4">
                  Clique numa variável para a inserir no template
                </p>
                <div className="space-y-2">
                  {TEMPLATE_VARIABLES.map((variable) => (
                    <button
                      key={variable.key}
                      type="button"
                      onClick={() => insertVariable(variable.key)}
                      className="w-full text-left px-3 py-2 text-xs bg-white border border-gray-200 rounded hover:bg-gray-50 hover:border-primary-black transition-colors"
                    >
                      <div className="font-medium text-gray-900">
                        {`{{${variable.key}}}`}
                      </div>
                      <div className="text-gray-600">{variable.label}</div>
                      <div className="text-gray-500 italic">
                        {variable.example}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-primary-black text-white rounded-md text-sm font-medium hover:bg-primary-mediumGray disabled:opacity-50"
            >
              {isLoading ? 'A guardar...' : template ? 'Atualizar' : 'Criar'}{' '}
              Template
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

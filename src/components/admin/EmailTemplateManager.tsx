'use client';

import { useState, useEffect } from 'react';
import { EmailTemplate } from '@/types';
import { supabase } from '@/lib/supabase';
import EmailTemplateEditor from './EmailTemplateEditor';
import { FadeIn } from '@/components/animations';

export default function EmailTemplateManager() {
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showEditor, setShowEditor] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<
    EmailTemplate | undefined
  >();
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    if (!supabase) return;

    try {
      const { data, error } = await supabase
        .from('email_templates')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading templates:', error);
        return;
      }

      setTemplates(data || []);
    } catch (error) {
      console.error('Error loading templates:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateNew = () => {
    setEditingTemplate(undefined);
    setShowEditor(true);
  };

  const handleEdit = (template: EmailTemplate) => {
    setEditingTemplate(template);
    setShowEditor(true);
  };

  const handleSave = (savedTemplate: EmailTemplate) => {
    if (editingTemplate) {
      // Update existing template in list
      setTemplates((prev) =>
        prev.map((t) => (t.id === savedTemplate.id ? savedTemplate : t))
      );
    } else {
      // Add new template to list
      setTemplates((prev) => [savedTemplate, ...prev]);
    }
    setShowEditor(false);
    setEditingTemplate(undefined);
  };

  const handleCancel = () => {
    setShowEditor(false);
    setEditingTemplate(undefined);
  };

  const handleDelete = async (template: EmailTemplate) => {
    if (!supabase) return;

    if (
      !confirm(
        `Tem a certeza que deseja eliminar o template "${template.name}"?`
      )
    ) {
      return;
    }

    try {
      const { error } = await supabase
        .from('email_templates')
        .delete()
        .eq('id', template.id);

      if (error) {
        console.error('Error deleting template:', error);
        alert('Erro ao eliminar template: ' + error.message);
        return;
      }

      setTemplates((prev) => prev.filter((t) => t.id !== template.id));
    } catch (error) {
      console.error('Error deleting template:', error);
      alert('Erro ao eliminar template');
    }
  };

  const handleToggleActive = async (template: EmailTemplate) => {
    if (!supabase) return;

    try {
      const { data, error } = await supabase
        .from('email_templates')
        .update({
          is_active: !template.is_active,
          updated_at: new Date().toISOString(),
        })
        .eq('id', template.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating template:', error);
        alert('Erro ao atualizar template: ' + error.message);
        return;
      }

      if (data) {
        setTemplates((prev) => prev.map((t) => (t.id === data.id ? data : t)));
      }
    } catch (error) {
      console.error('Error updating template:', error);
      alert('Erro ao atualizar template');
    }
  };

  const filteredTemplates = templates.filter(
    (template) =>
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.template_type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getTemplateTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      booking_confirmation: 'Confirmação de Reserva',
      booking_cancellation: 'Cancelamento de Reserva',
      contact_response: 'Resposta a Contacto',
      admin_notification: 'Notificação Admin',
    };
    return labels[type] || type;
  };

  const getTemplateTypeBadgeColor = (type: string) => {
    const colors: Record<string, string> = {
      booking_confirmation: 'bg-green-100 text-green-800',
      booking_cancellation: 'bg-red-100 text-red-800',
      contact_response: 'bg-blue-100 text-blue-800',
      admin_notification: 'bg-purple-100 text-purple-800',
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  if (showEditor) {
    return (
      <FadeIn>
        <EmailTemplateEditor
          template={editingTemplate}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      </FadeIn>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-black"></div>
      </div>
    );
  }

  return (
    <FadeIn>
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">
              Templates de Email
            </h1>
            <p className="mt-2 text-sm text-gray-700">
              Gerir templates de email para confirmações de reserva e
              comunicações com clientes.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button
              type="button"
              onClick={handleCreateNew}
              className="inline-flex items-center justify-center rounded-md border border-transparent bg-primary-black px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-mediumGray focus:outline-none focus:ring-2 focus:ring-primary-black focus:ring-offset-2"
            >
              Novo Template
            </button>
          </div>
        </div>

        {/* Search Bar */}
        <div className="mt-6">
          <div className="max-w-md">
            <input
              type="text"
              placeholder="Pesquisar templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-black focus:ring-primary-black"
            />
          </div>
        </div>

        {/* Templates List */}
        <div className="mt-8">
          {filteredTemplates.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-500">
                {searchTerm
                  ? 'Nenhum template encontrado.'
                  : 'Nenhum template criado ainda.'}
              </div>
              {!searchTerm && (
                <button
                  onClick={handleCreateNew}
                  className="mt-4 text-primary-black hover:text-primary-mediumGray"
                >
                  Criar o primeiro template
                </button>
              )}
            </div>
          ) : (
            <div className="bg-white shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table className="w-full divide-y divide-gray-300">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4">
                      Nome
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                      Tipo
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/3">
                      Assunto
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                      Estado
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Atualizado
                    </th>
                    <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredTemplates.map((template, index) => (
                    <tr
                      key={template.id}
                      className="hover:bg-gray-50 animate-fade-in"
                      style={{
                        animationDelay: `${index * 0.1}s`,
                        animationFillMode: 'both',
                      }}
                    >
                      <td className="px-3 py-4">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {template.name}
                        </div>
                      </td>
                      <td className="px-3 py-4">
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTemplateTypeBadgeColor(template.template_type)}`}
                        >
                          {getTemplateTypeLabel(template.template_type)}
                        </span>
                      </td>
                      <td className="px-3 py-4">
                        <div
                          className="text-sm text-gray-900 truncate"
                          title={template.subject}
                        >
                          {template.subject}
                        </div>
                      </td>
                      <td className="px-3 py-4">
                        <button
                          onClick={() => handleToggleActive(template)}
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            template.is_active
                              ? 'bg-green-100 text-green-800 hover:bg-green-200'
                              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                          }`}
                        >
                          {template.is_active ? 'Ativo' : 'Inativo'}
                        </button>
                      </td>
                      <td className="px-3 py-4 text-sm text-gray-500">
                        {template.updated_at
                          ? new Date(template.updated_at).toLocaleDateString(
                              'pt-PT'
                            )
                          : new Date(
                              template.created_at || ''
                            ).toLocaleDateString('pt-PT')}
                      </td>
                      <td className="px-3 py-4 text-right">
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => handleEdit(template)}
                            className="text-primary-black hover:text-primary-mediumGray text-sm"
                          >
                            Editar
                          </button>
                          <button
                            onClick={() => handleDelete(template)}
                            className="text-red-600 hover:text-red-900 text-sm"
                          >
                            Eliminar
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </FadeIn>
  );
}

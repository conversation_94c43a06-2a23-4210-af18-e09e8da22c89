import { supabase } from './supabase';

export interface SendEmailRequest {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

export interface TemplateEmailRequest {
  to: string;
  template_id: string;
  booking_id?: string;
  variables?: Record<string, string>;
}

export interface SendEmailResponse {
  success: boolean;
  email_id?: string;
  message?: string;
  error?: string;
}

/**
 * Send an email directly via Supabase Edge Function
 */
export async function sendEmail(
  request: SendEmailRequest
): Promise<SendEmailResponse> {
  if (!supabase) {
    return {
      success: false,
      error: 'Supabase client not initialized',
    };
  }

  try {
    console.log('Sending email via Edge Function:', { to: request.to, subject: request.subject });

    const { data, error } = await supabase.functions.invoke('send-email', {
      body: request,
    });

    if (error) {
      console.error('Error calling send-email function:', error);
      return {
        success: false,
        error: error.message || 'Failed to send email',
      };
    }

    console.log('Email sent successfully via Edge Function:', data);
    return data as SendEmailResponse;
  } catch (error) {
    console.error('Error sending email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Send an email using a template (processes template on client side)
 */
export async function sendTemplateEmail(
  request: TemplateEmailRequest
): Promise<SendEmailResponse> {
  if (!supabase) {
    return {
      success: false,
      error: 'Supabase client not initialized',
    };
  }

  try {
    console.log('Processing template email:', { template_id: request.template_id, to: request.to });

    // Get email template
    const { data: template, error: templateError } = await supabase
      .from('email_templates')
      .select('*')
      .eq('id', request.template_id)
      .eq('is_active', true)
      .single();

    if (templateError || !template) {
      console.error('Template not found:', templateError);
      return {
        success: false,
        error: 'Template not found or inactive',
      };
    }

    // Get booking data if booking_id is provided
    let booking = null;
    if (request.booking_id) {
      const { data: bookingData, error: bookingError } = await supabase
        .from('bookings')
        .select(`
          *,
          vehicle:vehicles(name, year)
        `)
        .eq('id', request.booking_id)
        .single();

      if (bookingError) {
        console.error('Error fetching booking:', bookingError);
      } else {
        booking = bookingData;
      }
    }

    // Prepare template variables
    const templateVariables: Record<string, string> = {
      company_name: 'Vintage Marketing Portugal',
      company_phone: '+*********** 591',
      company_email: '<EMAIL>',
      ...request.variables,
    };

    // Add booking-specific variables if available
    if (booking) {
      templateVariables.customer_name = booking.customer_name;
      templateVariables.customer_email = booking.customer_email;
      templateVariables.customer_phone = booking.customer_phone || '';
      templateVariables.start_date = new Date(
        booking.start_date
      ).toLocaleDateString('pt-PT', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      templateVariables.end_date = new Date(
        booking.end_date
      ).toLocaleDateString('pt-PT', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      templateVariables.booking_id = `VMP-${booking.id.slice(0, 8).toUpperCase()}`;
      templateVariables.admin_notes = booking.admin_notes || '';
      templateVariables.total_price = booking.total_price
        ? `€${booking.total_price.toFixed(2)}`
        : '';

      if (booking.vehicle) {
        templateVariables.vehicle_name = booking.vehicle.name;
        templateVariables.vehicle_year = booking.vehicle.year.toString();
      }
    }

    // Replace template variables in subject and body
    let processedSubject = template.subject;
    let processedBody = template.body;

    Object.entries(templateVariables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      processedSubject = processedSubject.replace(regex, value);
      processedBody = processedBody.replace(regex, value);
    });

    console.log('Template processed, sending email...');

    // Send the processed email
    return sendEmail({
      to: request.to,
      subject: processedSubject,
      html: processedBody.replace(/\n/g, '<br>'),
      text: processedBody,
    });
  } catch (error) {
    console.error('Error processing template email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Send booking confirmation email
 */
export async function sendBookingConfirmationEmail(
  bookingId: string,
  customerEmail: string,
  templateId?: string,
  additionalVariables?: Record<string, string>
): Promise<SendEmailResponse> {
  // If no template ID provided, try to find the default booking confirmation template
  let finalTemplateId = templateId;

  if (!finalTemplateId) {
    if (!supabase) {
      return {
        success: false,
        error: 'Supabase client not initialized',
      };
    }

    const { data: template, error } = await supabase
      .from('email_templates')
      .select('id')
      .eq('template_type', 'booking_confirmation')
      .eq('is_active', true)
      .limit(1)
      .single();

    if (error || !template) {
      return {
        success: false,
        error: 'No active booking confirmation template found',
      };
    }

    finalTemplateId = template.id;
  }

  return sendTemplateEmail({
    to: customerEmail,
    template_id: finalTemplateId,
    booking_id: bookingId,
    variables: additionalVariables,
  });
}

/**
 * Send booking cancellation email
 */
export async function sendBookingCancellationEmail(
  bookingId: string,
  customerEmail: string,
  templateId?: string,
  additionalVariables?: Record<string, string>
): Promise<SendEmailResponse> {
  // If no template ID provided, try to find the default booking cancellation template
  let finalTemplateId = templateId;

  if (!finalTemplateId) {
    if (!supabase) {
      return {
        success: false,
        error: 'Supabase client not initialized',
      };
    }

    const { data: template, error } = await supabase
      .from('email_templates')
      .select('id')
      .eq('template_type', 'booking_cancellation')
      .eq('is_active', true)
      .limit(1)
      .single();

    if (error || !template) {
      return {
        success: false,
        error: 'No active booking cancellation template found',
      };
    }

    finalTemplateId = template.id;
  }

  return sendTemplateEmail({
    to: customerEmail,
    template_id: finalTemplateId,
    booking_id: bookingId,
    variables: additionalVariables,
  });
}

/**
 * Send contact form response email
 */
export async function sendContactResponseEmail(
  customerEmail: string,
  customerName: string,
  templateId?: string,
  additionalVariables?: Record<string, string>
): Promise<SendEmailResponse> {
  // If no template ID provided, try to find the default contact response template
  let finalTemplateId = templateId;

  if (!finalTemplateId) {
    if (!supabase) {
      return {
        success: false,
        error: 'Supabase client not initialized',
      };
    }

    const { data: template, error } = await supabase
      .from('email_templates')
      .select('id')
      .eq('template_type', 'contact_response')
      .eq('is_active', true)
      .limit(1)
      .single();

    if (error || !template) {
      return {
        success: false,
        error: 'No active contact response template found',
      };
    }

    finalTemplateId = template.id;
  }

  return sendTemplateEmail({
    to: customerEmail,
    template_id: finalTemplateId,
    variables: {
      customer_name: customerName,
      customer_email: customerEmail,
      ...additionalVariables,
    },
  });
}

/**
 * Get all available email templates
 */
export async function getEmailTemplates() {
  if (!supabase) {
    return { data: null, error: 'Supabase client not initialized' };
  }

  return await supabase
    .from('email_templates')
    .select('*')
    .eq('is_active', true)
    .order('name');
}

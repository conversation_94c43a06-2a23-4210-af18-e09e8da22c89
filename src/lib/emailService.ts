import { supabase } from './supabase';

export interface SendEmailRequest {
  to: string;
  template_id: string;
  booking_id?: string;
  variables?: Record<string, string>;
}

export interface SendEmailResponse {
  success: boolean;
  email_id?: string;
  message?: string;
  error?: string;
}

/**
 * Send an email using a template via Supabase Edge Function
 */
export async function sendEmail(
  request: SendEmailRequest
): Promise<SendEmailResponse> {
  if (!supabase) {
    return {
      success: false,
      error: 'Supabase client not initialized',
    };
  }

  try {
    const { data, error } = await supabase.functions.invoke('send-email', {
      body: request,
    });

    if (error) {
      console.error('Error calling send-email function:', error);
      return {
        success: false,
        error: error.message || 'Failed to send email',
      };
    }

    return data as SendEmailResponse;
  } catch (error) {
    console.error('Error sending email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Send booking confirmation email
 */
export async function sendBookingConfirmationEmail(
  bookingId: string,
  customerEmail: string,
  templateId?: string,
  additionalVariables?: Record<string, string>
): Promise<SendEmailResponse> {
  // If no template ID provided, try to find the default booking confirmation template
  let finalTemplateId = templateId;

  if (!finalTemplateId) {
    if (!supabase) {
      return {
        success: false,
        error: 'Supabase client not initialized',
      };
    }

    const { data: template, error } = await supabase
      .from('email_templates')
      .select('id')
      .eq('template_type', 'booking_confirmation')
      .eq('is_active', true)
      .limit(1)
      .single();

    if (error || !template) {
      return {
        success: false,
        error: 'No active booking confirmation template found',
      };
    }

    finalTemplateId = template.id;
  }

  return sendEmail({
    to: customerEmail,
    template_id: finalTemplateId,
    booking_id: bookingId,
    variables: additionalVariables,
  });
}

/**
 * Send booking cancellation email
 */
export async function sendBookingCancellationEmail(
  bookingId: string,
  customerEmail: string,
  templateId?: string,
  additionalVariables?: Record<string, string>
): Promise<SendEmailResponse> {
  // If no template ID provided, try to find the default booking cancellation template
  let finalTemplateId = templateId;

  if (!finalTemplateId) {
    if (!supabase) {
      return {
        success: false,
        error: 'Supabase client not initialized',
      };
    }

    const { data: template, error } = await supabase
      .from('email_templates')
      .select('id')
      .eq('template_type', 'booking_cancellation')
      .eq('is_active', true)
      .limit(1)
      .single();

    if (error || !template) {
      return {
        success: false,
        error: 'No active booking cancellation template found',
      };
    }

    finalTemplateId = template.id;
  }

  return sendEmail({
    to: customerEmail,
    template_id: finalTemplateId,
    booking_id: bookingId,
    variables: additionalVariables,
  });
}

/**
 * Send contact form response email
 */
export async function sendContactResponseEmail(
  customerEmail: string,
  customerName: string,
  templateId?: string,
  additionalVariables?: Record<string, string>
): Promise<SendEmailResponse> {
  // If no template ID provided, try to find the default contact response template
  let finalTemplateId = templateId;

  if (!finalTemplateId) {
    if (!supabase) {
      return {
        success: false,
        error: 'Supabase client not initialized',
      };
    }

    const { data: template, error } = await supabase
      .from('email_templates')
      .select('id')
      .eq('template_type', 'contact_response')
      .eq('is_active', true)
      .limit(1)
      .single();

    if (error || !template) {
      return {
        success: false,
        error: 'No active contact response template found',
      };
    }

    finalTemplateId = template.id;
  }

  return sendEmail({
    to: customerEmail,
    template_id: finalTemplateId,
    variables: {
      customer_name: customerName,
      customer_email: customerEmail,
      ...additionalVariables,
    },
  });
}

/**
 * Get all available email templates
 */
export async function getEmailTemplates() {
  if (!supabase) {
    return { data: null, error: 'Supabase client not initialized' };
  }

  return await supabase
    .from('email_templates')
    .select('*')
    .eq('is_active', true)
    .order('name');
}

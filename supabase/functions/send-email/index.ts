import { createClient } from 'npm:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers':
    'authorization, x-client-info, apikey, content-type',
};

interface EmailRequest {
  to: string;
  template_id: string;
  booking_id?: string;
  variables?: Record<string, string>;
}

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  template_type: string;
  is_active: boolean;
}

interface Booking {
  id: string;
  customer_name: string;
  customer_email: string;
  customer_phone?: string;
  start_date: string;
  end_date: string;
  admin_notes?: string;
  total_price?: number;
  vehicle?: {
    name: string;
    year: number;
  };
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Parse request body
    const {
      to,
      template_id,
      booking_id,
      variables = {},
    }: EmailRequest = await req.json();

    if (!to || !template_id) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: to, template_id' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Get email template
    const { data: template, error: templateError } = await supabaseClient
      .from('email_templates')
      .select('*')
      .eq('id', template_id)
      .eq('is_active', true)
      .single();

    if (templateError || !template) {
      return new Response(
        JSON.stringify({ error: 'Template not found or inactive' }),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Get booking data if booking_id is provided
    let booking: Booking | null = null;
    if (booking_id) {
      const { data: bookingData, error: bookingError } = await supabaseClient
        .from('bookings')
        .select(
          `
          *,
          vehicle:vehicles(name, year)
        `
        )
        .eq('id', booking_id)
        .single();

      if (bookingError) {
        console.error('Error fetching booking:', bookingError);
      } else {
        booking = bookingData;
      }
    }

    // Prepare template variables
    const templateVariables: Record<string, string> = {
      company_name: 'Vintage Marketing Portugal',
      company_phone: '+*********** 591',
      company_email: '<EMAIL>',
      ...variables,
    };

    // Add booking-specific variables if available
    if (booking) {
      templateVariables.customer_name = booking.customer_name;
      templateVariables.customer_email = booking.customer_email;
      templateVariables.customer_phone = booking.customer_phone || '';
      templateVariables.start_date = new Date(
        booking.start_date
      ).toLocaleDateString('pt-PT', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      templateVariables.end_date = new Date(
        booking.end_date
      ).toLocaleDateString('pt-PT', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      templateVariables.booking_id = `VMP-${booking.id.slice(0, 8).toUpperCase()}`;
      templateVariables.admin_notes = booking.admin_notes || '';
      templateVariables.total_price = booking.total_price
        ? `€${booking.total_price.toFixed(2)}`
        : '';

      if (booking.vehicle) {
        templateVariables.vehicle_name = booking.vehicle.name;
        templateVariables.vehicle_year = booking.vehicle.year.toString();
      }
    }

    // Replace template variables in subject and body
    let processedSubject = template.subject;
    let processedBody = template.body;

    Object.entries(templateVariables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      processedSubject = processedSubject.replace(regex, value);
      processedBody = processedBody.replace(regex, value);
    });

    // Send email using Resend
    const resendApiKey = Deno.env.get('RESEND_API_KEY');
    if (!resendApiKey) {
      return new Response(
        JSON.stringify({ error: 'Resend API key not configured' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    const emailPayload = {
      from: 'Vintage Marketing Portugal <<EMAIL>>',
      to: [to],
      subject: processedSubject,
      html: processedBody.replace(/\n/g, '<br>'),
      text: processedBody,
    };

    const resendResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${resendApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailPayload),
    });

    if (!resendResponse.ok) {
      const resendError = await resendResponse.text();
      console.error('Resend API error:', resendError);
      return new Response(
        JSON.stringify({ error: 'Failed to send email', details: resendError }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    const resendResult = await resendResponse.json();

    // Log email sending (optional - you might want to create an email_logs table)
    console.log('Email sent successfully:', {
      to,
      template_id,
      booking_id,
      email_id: resendResult.id,
    });

    return new Response(
      JSON.stringify({
        success: true,
        email_id: resendResult.id,
        message: 'Email sent successfully',
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  } catch (error) {
    console.error('Error in send-email function:', error);
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        details: error.message,
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
